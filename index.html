<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="/kg.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Accure SecureGPT</title>
    
    <!-- Load CSS files -->
    <link rel="stylesheet" href="/assets/main.c519ac7b.css">
    <link rel="stylesheet" href="/assets/forms.640fb8f2.css">
    <link rel="stylesheet" href="/assets/AdminNavBar.c5b178b0.css">
    <link rel="stylesheet" href="/assets/FileUploader.d9ac2529.css">
    <link rel="stylesheet" href="/assets/pendingMessage.c6461223.css">
    <link rel="stylesheet" href="/assets/4.62dafba3.css">
    <link rel="stylesheet" href="/assets/5.00584e21.css">
    <link rel="stylesheet" href="/assets/7.f1d4aa1c.css">
    <link rel="stylesheet" href="/assets/12.02fd05f2.css">
    
    <!-- Preload key modules -->
    <link rel="modulepreload" href="/entry/start.5fd2019a.js">
    <link rel="modulepreload" href="/entry/app.b53eae29.js">
</head>
<body data-sveltekit-preload-data="hover">
    <div style="display: contents">
        <div id="svelte">
            <!-- SvelteKit app will mount here -->
            <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
                <h1>Loading Accure SecureGPT...</h1>
                <p>Please wait while the application loads.</p>
                <div style="margin: 20px 0;">
                    <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                </div>
                <p style="color: #666; font-size: 14px;">
                    Backend API: <a href="http://localhost:9001/health" target="_blank">http://localhost:9001</a>
                </p>
            </div>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .error-message {
            padding: 20px;
            text-align: center;
            color: #d32f2f;
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
            border-radius: 4px;
            margin: 20px;
        }
        
        .success-message {
            padding: 20px;
            text-align: center;
            color: #2e7d32;
            background-color: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 4px;
            margin: 20px;
        }
    </style>

    <!-- Load the SvelteKit application -->
    <script type="module">
        console.log('🚀 Starting Accure SecureGPT Frontend...');
        
        // Set up the environment
        window.__sveltekit = {
            base: '',
            assets: '',
            env: {
                PUBLIC_API_URL: 'http://localhost:9001'
            }
        };
        
        // Test backend connectivity first
        async function testBackendConnection() {
            try {
                const response = await fetch('http://localhost:9001/health');
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ Backend connection successful:', data);
                    return true;
                } else {
                    throw new Error(`Backend returned status ${response.status}`);
                }
            } catch (error) {
                console.error('❌ Backend connection failed:', error);
                return false;
            }
        }
        
        // Load the SvelteKit app
        async function loadApp() {
            try {
                console.log('🔍 Testing backend connection...');
                const backendOk = await testBackendConnection();
                
                if (!backendOk) {
                    throw new Error('Backend server is not responding. Please ensure the backend is running on port 9001.');
                }
                
                console.log('📦 Loading SvelteKit modules...');
                
                // Load the start module
                const startModule = await import('/entry/start.5fd2019a.js');
                console.log('✅ SvelteKit app loaded successfully');
                
                // Show success message briefly
                document.getElementById('svelte').innerHTML = `
                    <div class="success-message">
                        <h2>✅ Application Loaded Successfully!</h2>
                        <p>Frontend and backend are connected and working.</p>
                        <p>Initializing the chat interface...</p>
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Failed to load SvelteKit app:', error);
                document.getElementById('svelte').innerHTML = `
                    <div class="error-message">
                        <h2>❌ Failed to Load Application</h2>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Please check the following:</p>
                        <ul style="text-align: left; display: inline-block;">
                            <li>Backend server is running on <a href="http://localhost:9001/health" target="_blank">http://localhost:9001</a></li>
                            <li>No CORS issues in the browser console</li>
                            <li>All required files are present</li>
                        </ul>
                        <p>
                            <button onclick="location.reload()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                Retry
                            </button>
                        </p>
                    </div>
                `;
            }
        }
        
        // Start loading the app
        loadApp();
    </script>
</body>
</html>
