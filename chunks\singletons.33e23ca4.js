import{w as u,a as v}from"./paths.e40a44b0.js";const k="1750265067184",A="sveltekit:snapshot",R="sveltekit:scroll",y="sveltekit:index",f={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},_=location.origin;function I(e){let t=e.baseURI;if(!t){const n=e.getElementsByTagName("base");t=n.length?n[0].href:e.URL}return t}function S(){return{x:pageXOffset,y:pageYOffset}}function c(e,t){return e.getAttribute(`data-sveltekit-${t}`)}const d={...f,"":f.hover};function h(e){let t=e.assignedSlot??e.parentNode;return(t==null?void 0:t.nodeType)===11&&(t=t.host),t}function T(e,t){for(;e&&e!==t;){if(e.nodeName.toUpperCase()==="A"&&e.hasAttribute("href"))return e;e=h(e)}}function x(e,t){let n;try{n=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI)}catch{}const o=e instanceof SVGAElement?e.target.baseVal:e.target,r=!n||!!o||w(n,t)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),l=(n==null?void 0:n.origin)===_&&e.hasAttribute("download");return{url:n,external:r,target:o,download:l}}function O(e){let t=null,n=null,o=null,r=null,l=null,s=null,a=e;for(;a&&a!==document.documentElement;)o===null&&(o=c(a,"preload-code")),r===null&&(r=c(a,"preload-data")),t===null&&(t=c(a,"keepfocus")),n===null&&(n=c(a,"noscroll")),l===null&&(l=c(a,"reload")),s===null&&(s=c(a,"replacestate")),a=h(a);function i(b){switch(b){case"":case"true":return!0;case"off":case"false":return!1;default:return null}}return{preload_code:d[o??"off"],preload_data:d[r??"off"],keep_focus:i(t),noscroll:i(n),reload:i(l),replace_state:i(s)}}function p(e){const t=u(e);let n=!0;function o(){n=!0,t.update(s=>s)}function r(s){n=!1,t.set(s)}function l(s){let a;return t.subscribe(i=>{(a===void 0||n&&i!==a)&&s(a=i)})}return{notify:o,set:r,subscribe:l}}function m(){const{set:e,subscribe:t}=u(!1);let n;async function o(){clearTimeout(n);try{const r=await fetch(`${v}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!r.ok)return!1;const s=(await r.json()).version!==k;return s&&(e(!0),clearTimeout(n)),s}catch{return!1}}return{subscribe:t,check:o}}function w(e,t){return e.origin!==_||!e.pathname.startsWith(t)}let g;function U(e){g=e.client}function L(e){return(...t)=>g[e](...t)}const N={url:p({}),page:p({}),navigating:u(null),updated:m()};export{y as I,f as P,R as S,A as a,x as b,L as c,O as d,S as e,T as f,I as g,U as h,w as i,g as j,_ as o,N as s};
