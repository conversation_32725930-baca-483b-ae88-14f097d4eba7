# Accure SecureGPT - Complete Full-Stack Application

A complete full-stack implementation of the Accure SecureGPT chat application with a Node.js/Express backend and SvelteKit frontend.

## 🏗️ Architecture

### Backend (Node.js/Express)
- **Framework**: Express.js with TypeScript-style organization
- **Database**: SQLite for development (easily upgradeable to PostgreSQL/MySQL)
- **Authentication**: JWT-based authentication system
- **File Upload**: Multer-based file handling with support for multiple formats
- **Security**: Helmet, CORS, rate limiting, input validation
- **API**: RESTful API design with comprehensive error handling

### Frontend (SvelteKit)
- **Framework**: SvelteKit (pre-built/compiled version)
- **Styling**: Custom CSS with responsive design
- **Features**: Chat interface, file upload, user management
- **API Integration**: Axios-based HTTP client for backend communication

## 📁 Project Structure

```
iq.accure.ai/
├── backend/                 # Backend server
│   ├── config/
│   │   └── database.js     # Database configuration and connection
│   ├── middleware/
│   │   └── auth.js         # Authentication middleware
│   ├── routes/
│   │   ├── auth.js         # Authentication endpoints
│   │   ├── chat.js         # Chat/conversation endpoints
│   │   ├── files.js        # File upload/management endpoints
│   │   └── settings.js     # User settings endpoints
│   ├── scripts/
│   │   └── init-db.js      # Database initialization script
│   ├── data/               # SQLite database files
│   ├── uploads/            # User uploaded files
│   ├── .env                # Environment configuration
│   ├── package.json        # Backend dependencies
│   ├── server.js           # Main server file
│   └── test-api.js         # API testing script
├── assets/                 # Frontend CSS assets
├── chunks/                 # SvelteKit compiled chunks
├── entry/                  # SvelteKit entry points
├── nodes/                  # SvelteKit route nodes
├── index.html              # Frontend entry point
├── serve-frontend.js       # Frontend server script
└── README.md               # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Python 3 (for serving frontend)

### 1. Setup Backend

```bash
cd backend
npm install
npm run init-db
npm run dev
```

The backend will start on `http://localhost:9001`

### 2. Setup Frontend

```bash
# From the root directory
python -m http.server 3000
```

The frontend will be available at `http://localhost:3000`

### 3. Test the Application

Open your browser and navigate to:
- **Frontend**: http://localhost:3000
- **Backend Health**: http://localhost:9001/health
- **API Documentation**: http://localhost:9001/api

## 🔐 Default Users

The system comes with pre-configured test users:

| Email | Password | Role |
|-------|----------|------|
| <EMAIL> | admin123 | admin |
| <EMAIL> | test123 | user |

## 📡 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/logout` - User logout
- `GET /api/auth/verify` - Verify JWT token

### Chat & Conversations
- `GET /api/chat/conversations` - Get user conversations
- `POST /api/chat/conversations` - Create new conversation
- `GET /api/chat/conversations/:id` - Get specific conversation
- `POST /api/chat/conversations/:id/messages` - Add message to conversation
- `DELETE /api/chat/conversations/:id` - Delete conversation

### File Management
- `POST /api/files/upload` - Upload single file
- `POST /api/files/upload-multiple` - Upload multiple files
- `GET /api/files` - Get user files (paginated)
- `GET /api/files/:id` - Get file metadata
- `GET /api/files/:id/download` - Download file
- `DELETE /api/files/:id` - Delete file

### Settings & User Management
- `GET /api/settings` - Get user settings
- `PUT /api/settings` - Update user settings
- `POST /api/settings/reset` - Reset settings to default
- `GET /api/settings/models` - Get available AI models
- `GET /api/settings/context-modes` - Get context modes
- `GET /api/settings/stats` - Get user statistics
- `GET /api/settings/export` - Export user data

## 🗄️ Database Schema

### Users Table
- `id` - Primary key
- `email` - Unique user email
- `name` - User display name
- `org` - Organization name
- `role` - User role (user/admin)
- `password_hash` - Bcrypt hashed password
- `created_at`, `updated_at` - Timestamps

### Conversations Table
- `id` - UUID primary key
- `user_id` - Foreign key to users
- `title` - Conversation title
- `created_at`, `updated_at` - Timestamps

### Messages Table
- `id` - UUID primary key
- `conversation_id` - Foreign key to conversations
- `content` - Message content
- `role` - Message role (user/assistant/system)
- `created_at` - Timestamp

### Files Table
- `id` - UUID primary key
- `user_id` - Foreign key to users
- `filename` - Stored filename
- `original_name` - Original filename
- `mime_type` - File MIME type
- `size` - File size in bytes
- `path` - File storage path
- `created_at` - Timestamp

### Settings Table
- `id` - Primary key
- `user_id` - Foreign key to users
- `context_mode` - AI context mode
- `custom_prompt` - Custom system prompt
- `model_name` - Selected AI model
- `created_at`, `updated_at` - Timestamps

## 🔧 Configuration

### Environment Variables (.env)

```env
# Server Configuration
PORT=9001
NODE_ENV=development

# Database Configuration
DB_PATH=./data/database.sqlite

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=52428800

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 🧪 Testing

### Backend API Testing
```bash
cd backend
node test-api.js
```

This will run comprehensive tests on all API endpoints.

### Manual Testing
1. Start both backend and frontend servers
2. Open http://localhost:3000 in your browser
3. Login with test credentials
4. Test chat functionality, file uploads, and settings

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Bcrypt for secure password storage
- **Input Validation**: Express-validator for request validation
- **Rate Limiting**: Protection against brute force attacks
- **CORS Configuration**: Proper cross-origin resource sharing
- **File Upload Security**: MIME type validation and size limits
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Helmet.js security headers

## 🚀 Production Deployment

### Backend Deployment
1. Set `NODE_ENV=production`
2. Use a production database (PostgreSQL/MySQL)
3. Configure proper JWT secrets
4. Set up reverse proxy (nginx)
5. Enable HTTPS
6. Configure proper CORS origins

### Frontend Deployment
1. Build the SvelteKit app for production
2. Serve static files through a CDN
3. Configure proper API endpoints
4. Enable HTTPS

## 📝 Development Notes

### Adding New Features
1. **Backend**: Add routes in `/routes`, update database schema if needed
2. **Frontend**: Modify SvelteKit components (requires rebuild)
3. **Database**: Create migration scripts for schema changes

### File Upload Support
Currently supports:
- **Text**: .txt, .pdf, .docx, .pptx
- **Images**: .png, .jpg, .jpeg, .webp
- **Video**: .mp4, .mov, .avi, .mkv
- **Audio**: .mp3, .wav, .aac
- **Data**: .csv, .tsv, .json

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **Backend won't start**: Check if port 9001 is available
2. **Database errors**: Run `npm run init-db` to reset database
3. **Frontend not loading**: Ensure Python HTTP server is running on port 3000
4. **CORS errors**: Check FRONTEND_URL in .env file
5. **File upload fails**: Check UPLOAD_DIR permissions and MAX_FILE_SIZE

### Support

For issues and questions, please check the API logs and browser console for detailed error messages.
