import{s as Fe,e as _,a as g,c as d,n as T,f as C,p as A,d as m,g as u,A as Ce,y as U,j as be,i as l,B as X,r as H,C as Re,z as ke,v as Ge,t as ze,q as Me,x as je,D as xe}from"../chunks/scheduler.91cfa29b.js";import{S as He,i as Je,c as Ke,a as Qe,m as We,t as Xe,b as Ye,d as Ze}from"../chunks/index.03cf2e9a.js";import{e as ae}from"../chunks/each.6f0e5b78.js";import{s as $e}from"../chunks/sessionStore.520658ac.js";import{A as et}from"../chunks/AdminNavBar.18314955.js";import{g as Be}from"../chunks/navigation.90364bb7.js";import{a as tt}from"../chunks/axios.1f2f0c0e.js";function qe(i,e,a){const r=i.slice();return r[12]=e[a].name,r[13]=e[a].description,r}function we(i,e,a){const r=i.slice();return r[16]=e[a].group,r[13]=e[a].description,r}function De(i){let e,a=i[16]+"",r,n;return{c(){e=_("option"),r=ze(a),this.h()},l(c){e=d(c,"OPTION",{});var s=T(e);r=Me(s,a),s.forEach(m),this.h()},h(){e.__value=n=i[16],U(e,e.__value)},m(c,s){be(c,e,s),l(e,r)},p(c,s){s&1&&a!==(a=c[16]+"")&&je(r,a),s&1&&n!==(n=c[16])&&(e.__value=n,U(e,e.__value))},d(c){c&&m(e)}}}function Ve(i){let e,a=i[13]+"",r,n;return{c(){e=_("option"),r=ze(a),this.h()},l(c){e=d(c,"OPTION",{});var s=T(e);r=Me(s,a),s.forEach(m),this.h()},h(){e.__value=n=i[12],U(e,e.__value)},m(c,s){be(c,e,s),l(e,r)},p(c,s){s&1&&a!==(a=c[13]+"")&&je(r,a),s&1&&n!==(n=c[12])&&(e.__value=n,U(e,e.__value))},d(c){c&&m(e)}}}function st(i){let e,a,r,n,c,s,x,Y,S,$="Edit User",Z,L,B="Full Name:",ne,y,re,q,Ee="Email:",oe,N,ie,O,w,D,Ie="Group:",ue,b,ce,V,z,Te="Role:",_e,E,de,M,j,Ne="Status:",fe,I,P,Ue="Active",k,ye="Inactive",he,ve,F,Le="⠀Save⠀",pe,R,Oe="Cancel",ee,me,Ae;a=new et({});let K=ae(i[0].groups),h=[];for(let t=0;t<K.length;t+=1)h[t]=De(we(i,K,t));let Q=ae(i[0].roles),v=[];for(let t=0;t<Q.length;t+=1)v[t]=Ve(qe(i,Q,t));return{c(){e=_("main"),Ke(a.$$.fragment),r=g(),n=_("div"),c=_("div"),s=_("form"),x=_("input"),Y=g(),S=_("span"),S.textContent=$,Z=g(),L=_("label"),L.textContent=B,ne=g(),y=_("input"),re=g(),q=_("label"),q.textContent=Ee,oe=g(),N=_("input"),ie=g(),O=_("div"),w=_("div"),D=_("label"),D.textContent=Ie,ue=g(),b=_("select");for(let t=0;t<h.length;t+=1)h[t].c();ce=g(),V=_("div"),z=_("label"),z.textContent=Te,_e=g(),E=_("select");for(let t=0;t<v.length;t+=1)v[t].c();de=g(),M=_("div"),j=_("label"),j.textContent=Ne,fe=g(),I=_("select"),P=_("option"),P.textContent=Ue,k=_("option"),k.textContent=ye,he=g(),ve=g(),F=_("button"),F.textContent=Le,pe=g(),R=_("button"),R.textContent=Oe,this.h()},l(t){e=d(t,"MAIN",{});var p=T(e);Qe(a.$$.fragment,p),r=C(p),n=d(p,"DIV",{id:!0,class:!0});var o=T(n);c=d(o,"DIV",{class:!0});var G=T(c);s=d(G,"FORM",{method:!0});var f=T(s);x=d(f,"INPUT",{type:!0,id:!0,class:!0}),Y=C(f),S=d(f,"SPAN",{class:!0,"data-svelte-h":!0}),A(S)!=="svelte-1lkoup9"&&(S.textContent=$),Z=C(f),L=d(f,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),A(L)!=="svelte-oy9dtf"&&(L.textContent=B),ne=C(f),y=d(f,"INPUT",{type:!0,id:!0,class:!0}),re=C(f),q=d(f,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),A(q)!=="svelte-164u6lo"&&(q.textContent=Ee),oe=C(f),N=d(f,"INPUT",{type:!0,id:!0,class:!0}),ie=C(f),O=d(f,"DIV",{class:!0});var W=T(O);w=d(W,"DIV",{class:!0});var te=T(w);D=d(te,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),A(D)!=="svelte-i60fxe"&&(D.textContent=Ie),ue=C(te),b=d(te,"SELECT",{id:!0,class:!0});var Se=T(b);for(let J=0;J<h.length;J+=1)h[J].l(Se);Se.forEach(m),te.forEach(m),ce=C(W),V=d(W,"DIV",{class:!0});var se=T(V);z=d(se,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),A(z)!=="svelte-1gy65ts"&&(z.textContent=Te),_e=C(se),E=d(se,"SELECT",{id:!0,class:!0});var Pe=T(E);for(let J=0;J<v.length;J+=1)v[J].l(Pe);Pe.forEach(m),se.forEach(m),de=C(W),M=d(W,"DIV",{class:!0});var le=T(M);j=d(le,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),A(j)!=="svelte-z6z77o"&&(j.textContent=Ne),fe=C(le),I=d(le,"SELECT",{id:!0,class:!0});var ge=T(I);P=d(ge,"OPTION",{"data-svelte-h":!0}),A(P)!=="svelte-1mh57he"&&(P.textContent=Ue),k=d(ge,"OPTION",{"data-svelte-h":!0}),A(k)!=="svelte-okme9y"&&(k.textContent=ye),ge.forEach(m),le.forEach(m),W.forEach(m),he=C(f),ve=C(f),F=d(f,"BUTTON",{type:!0,class:!0,"data-svelte-h":!0}),A(F)!=="svelte-10lct8c"&&(F.textContent=Le),pe=C(f),R=d(f,"BUTTON",{class:!0,"data-svelte-h":!0}),A(R)!=="svelte-zzwmmu"&&(R.textContent=Oe),f.forEach(m),G.forEach(m),o.forEach(m),p.forEach(m),this.h()},h(){u(x,"type","text"),u(x,"id","hfUserId"),x.required=!0,x.hidden=!0,u(x,"class","svelte-1s4inx2"),u(S,"class","modal-header svelte-1s4inx2"),u(L,"for","firstName"),u(L,"class","svelte-1s4inx2"),u(y,"type","text"),u(y,"id","name"),y.required=!0,u(y,"class","svelte-1s4inx2"),u(q,"for","username"),u(q,"class","svelte-1s4inx2"),u(N,"type","text"),u(N,"id","username"),N.required=!0,N.readOnly=!0,u(N,"class","svelte-1s4inx2"),u(D,"for","group"),u(D,"class","svelte-1s4inx2"),u(b,"id","group"),b.required=!0,u(b,"class","svelte-1s4inx2"),i[1].group===void 0&&Ce(()=>i[7].call(b)),u(w,"class","dropdown svelte-1s4inx2"),u(z,"for","type"),u(z,"class","svelte-1s4inx2"),u(E,"id","type"),E.required=!0,u(E,"class","svelte-1s4inx2"),i[1].role===void 0&&Ce(()=>i[8].call(E)),u(V,"class","dropdown svelte-1s4inx2"),u(j,"for","status"),u(j,"class","svelte-1s4inx2"),P.__value="active",U(P,P.__value),k.__value="inactive",U(k,k.__value),u(I,"id","status"),I.required=!0,u(I,"class","svelte-1s4inx2"),i[1].status===void 0&&Ce(()=>i[9].call(I)),u(M,"class","dropdown svelte-1s4inx2"),u(O,"class","dropdown-container svelte-1s4inx2"),u(F,"type","submit"),u(F,"class","svelte-1s4inx2"),u(R,"class","svelte-1s4inx2"),u(s,"method","POST"),u(c,"class","modal-content svelte-1s4inx2"),u(n,"id","editModal"),u(n,"class","modal-container svelte-1s4inx2")},m(t,p){be(t,e,p),We(a,e,null),l(e,r),l(e,n),l(n,c),l(c,s),l(s,x),U(x,i[1].hfUserId),l(s,Y),l(s,S),l(s,Z),l(s,L),l(s,ne),l(s,y),U(y,i[1].name),l(s,re),l(s,q),l(s,oe),l(s,N),U(N,i[1].email),l(s,ie),l(s,O),l(O,w),l(w,D),l(w,ue),l(w,b);for(let o=0;o<h.length;o+=1)h[o]&&h[o].m(b,null);X(b,i[1].group,!0),l(O,ce),l(O,V),l(V,z),l(V,_e),l(V,E);for(let o=0;o<v.length;o+=1)v[o]&&v[o].m(E,null);X(E,i[1].role,!0),l(O,de),l(O,M),l(M,j),l(M,fe),l(M,I),l(I,P),l(I,k),X(I,i[1].status,!0),l(s,he),l(s,ve),l(s,F),l(s,pe),l(s,R),ee=!0,me||(Ae=[H(x,"input",i[4]),H(y,"input",i[5]),H(N,"input",i[6]),H(b,"change",i[7]),H(E,"change",i[8]),H(I,"change",i[9]),H(R,"click",i[3]),H(s,"submit",Re(i[2]))],me=!0)},p(t,[p]){if(p&3&&x.value!==t[1].hfUserId&&U(x,t[1].hfUserId),p&3&&y.value!==t[1].name&&U(y,t[1].name),p&3&&N.value!==t[1].email&&U(N,t[1].email),p&1){K=ae(t[0].groups);let o;for(o=0;o<K.length;o+=1){const G=we(t,K,o);h[o]?h[o].p(G,p):(h[o]=De(G),h[o].c(),h[o].m(b,null))}for(;o<h.length;o+=1)h[o].d(1);h.length=K.length}if(p&3&&X(b,t[1].group),p&1){Q=ae(t[0].roles);let o;for(o=0;o<Q.length;o+=1){const G=qe(t,Q,o);v[o]?v[o].p(G,p):(v[o]=Ve(G),v[o].c(),v[o].m(E,null))}for(;o<v.length;o+=1)v[o].d(1);v.length=Q.length}p&3&&X(E,t[1].role),p&3&&X(I,t[1].status)},i(t){ee||(Xe(a.$$.fragment,t),ee=!0)},o(t){Ye(a.$$.fragment,t),ee=!1},d(t){t&&m(e),Ze(a),ke(h,t),ke(v,t),me=!1,Ge(Ae)}}}function lt(i,e,a){let{data:r}=e,n={hfUserId:"",name:"",email:"",username:"",password:"",role:"",status:"",group:""};r&&(n.hfUserId=r.users.hfUserId,n.name=r.users.name,n.email=r.users.email,n.username=r.users.username,n.role=r.users.role,n.status=r.users.status,n.group=r.users.group),$e.subscribe(B=>{B.email,B.role});async function c(){await tt.post("/admin/users/update",n,{headers:{"Content-Type":"application/json"}}),Be("/admin/users")}function s(){Be("/admin/users")}function x(){n.hfUserId=this.value,a(1,n),a(0,r)}function Y(){n.name=this.value,a(1,n),a(0,r)}function S(){n.email=this.value,a(1,n),a(0,r)}function $(){n.group=xe(this),a(1,n),a(0,r)}function Z(){n.role=xe(this),a(1,n),a(0,r)}function L(){n.status=xe(this),a(1,n),a(0,r)}return i.$$set=B=>{"data"in B&&a(0,r=B.data)},[r,n,c,s,x,Y,S,$,Z,L]}class _t extends He{constructor(e){super(),Je(this,e,lt,st,Fe,{data:0})}}export{_t as component};
