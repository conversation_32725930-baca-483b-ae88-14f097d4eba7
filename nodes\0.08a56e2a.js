import{s as fe,a6 as ie,K as ce,H as Se,L as ue,n as A,E as Ne,d as u,a7 as ve,j as H,u as ne,a8 as pe,a9 as rt,e as w,a as I,t as xe,c as y,f as B,q as $e,g as o,i as m,r as ae,x as Le,aa as nt,ab as lt,ac as st,v as _e,l as Ce,ae as He,b as je,p as re,C as Ee,o as at,F as Me,O as Be,R as Ae,A as vt,af as ot,h as pt,ad as _t,m as be}from"../chunks/scheduler.91cfa29b.js";import{S as de,i as he,c as J,a as X,m as Y,t as j,b as V,d as ee,g as we,f as ye,h as Pe}from"../chunks/index.03cf2e9a.js";import{b as it,c as ct,d as bt,e as me,g as kt,f as wt}from"../chunks/public.44f51b11.js";import{i as Ve,g as yt}from"../chunks/navigation.90364bb7.js";import{n as xt,p as ut}from"../chunks/stores.33557e05.js";/* empty css                     */import{b as F}from"../chunks/paths.e40a44b0.js";import"../chunks/index.3feb4388.js";import{U as ze,s as Ue,t as Ge}from"../chunks/titleUpdate.4d61826c.js";import{g as Oe,C as Ie,a as $t,b as Tt,L as ft,f as Fe,M as dt,c as ht,e as ke}from"../chunks/forms.ab03a9be.js";import{e as Re,u as Ct,o as Et}from"../chunks/each.6f0e5b78.js";const Lt=!0,Mt=Lt;function At(l){let e,t,r='<path fill="currentColor" d="M17 15V8h-2v7H8v2h7v7h2v-7h7v-2z"/>',s=[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},l[0]],n={};for(let a=0;a<s.length;a+=1)n=ie(n,s[a]);return{c(){e=ce("svg"),t=new Se(!0),this.h()},l(a){e=ue(a,"svg",{viewBox:!0,width:!0,height:!0});var i=A(e);t=Ne(i,!0),i.forEach(u),this.h()},h(){t.a=null,ve(e,n)},m(a,i){H(a,e,i),t.m(r,e)},p(a,[i]){ve(e,n=Oe(s,[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},i&1&&a[0]]))},i:ne,o:ne,d(a){a&&u(e)}}}function St(l,e,t){return l.$$set=r=>{t(0,e=ie(ie({},e),pe(r)))},e=pe(e),[e]}class Nt extends de{constructor(e){super(),he(this,e,St,At,fe,{})}}function Ht(l){let e,t,r='<path fill="currentColor" d="M6 6h20v2H6zm0 6h20v2H6zm0 6h20v2H6zm0 6h20v2H6z"/>',s=[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},l[0]],n={};for(let a=0;a<s.length;a+=1)n=ie(n,s[a]);return{c(){e=ce("svg"),t=new Se(!0),this.h()},l(a){e=ue(a,"svg",{viewBox:!0,width:!0,height:!0});var i=A(e);t=Ne(i,!0),i.forEach(u),this.h()},h(){t.a=null,ve(e,n)},m(a,i){H(a,e,i),t.m(r,e)},p(a,[i]){ve(e,n=Oe(s,[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},i&1&&a[0]]))},i:ne,o:ne,d(a){a&&u(e)}}}function Ot(l,e,t){return l.$$set=r=>{t(0,e=ie(ie({},e),pe(r)))},e=pe(e),[e]}class It extends de{constructor(e){super(),he(this,e,Ot,Ht,fe,{})}}function Bt(l){let e,t,r,s,n,a,i,f,c,d,g,b,v,k,x,$,M,p,E;r=new It({}),c=new Nt({}),k=new Ie({});const P=l[7].default,z=rt(P,l,l[6],null);return{c(){e=w("nav"),t=w("button"),J(r.$$.fragment),s=I(),n=w("span"),a=xe(l[0]),i=I(),f=w("a"),J(c.$$.fragment),d=I(),g=w("nav"),b=w("div"),v=w("button"),J(k.$$.fragment),x=I(),z&&z.c(),this.h()},l(C){e=y(C,"NAV",{class:!0});var _=A(e);t=y(_,"BUTTON",{type:!0,class:!0,"aria-label":!0});var D=A(t);X(r.$$.fragment,D),D.forEach(u),s=B(_),n=y(_,"SPAN",{class:!0});var S=A(n);a=$e(S,l[0]),S.forEach(u),i=B(_),f=y(_,"A",{href:!0,class:!0});var U=A(f);X(c.$$.fragment,U),U.forEach(u),_.forEach(u),d=B(C),g=y(C,"NAV",{class:!0});var O=A(g);b=y(O,"DIV",{class:!0});var te=A(b);v=y(te,"BUTTON",{type:!0,class:!0,"aria-label":!0});var se=A(v);X(k.$$.fragment,se),se.forEach(u),te.forEach(u),x=B(O),z&&z.l(O),O.forEach(u),this.h()},h(){o(t,"type","button"),o(t,"class","-ml-3 flex h-9 w-9 shrink-0 items-center justify-center"),o(t,"aria-label","Open menu"),o(n,"class","truncate px-4"),o(f,"href",`${F}/`),o(f,"class","-mr-3 flex h-9 w-9 shrink-0 items-center justify-center"),o(e,"class","flex h-12 items-center justify-between border-b bg-gray-50 px-4 dark:border-gray-800 dark:bg-gray-800/70 md:hidden"),o(v,"type","button"),o(v,"class","-mr-3 ml-auto flex h-9 w-9 items-center justify-center"),o(v,"aria-label","Close menu"),o(b,"class","flex h-12 items-center px-4"),o(g,"class",$="fixed inset-0 z-30 grid max-h-screen grid-cols-1 grid-rows-[auto,auto,1fr,auto] bg-white bg-gradient-to-l from-gray-50 dark:bg-gray-900 dark:from-gray-800/30 max-sm:rounded-t-2xl "+(l[1]?"block":"hidden"))},m(C,_){H(C,e,_),m(e,t),Y(r,t,null),l[9](t),m(e,s),m(e,n),m(n,a),m(e,i),m(e,f),Y(c,f,null),H(C,d,_),H(C,g,_),m(g,b),m(b,v),Y(k,v,null),l[11](v),m(g,x),z&&z.m(g,null),M=!0,p||(E=[ae(t,"click",l[8]),ae(v,"click",l[10])],p=!0)},p(C,[_]){(!M||_&1)&&Le(a,C[0]),z&&z.p&&(!M||_&64)&&nt(z,P,C,C[6],M?st(P,C[6],_,null):lt(C[6]),null),(!M||_&2&&$!==($="fixed inset-0 z-30 grid max-h-screen grid-cols-1 grid-rows-[auto,auto,1fr,auto] bg-white bg-gradient-to-l from-gray-50 dark:bg-gray-900 dark:from-gray-800/30 max-sm:rounded-t-2xl "+(C[1]?"block":"hidden")))&&o(g,"class",$)},i(C){M||(j(r.$$.fragment,C),j(c.$$.fragment,C),j(k.$$.fragment,C),j(z,C),M=!0)},o(C){V(r.$$.fragment,C),V(c.$$.fragment,C),V(k.$$.fragment,C),V(z,C),M=!1},d(C){C&&(u(e),u(d),u(g)),ee(r),l[9](null),ee(c),ee(k),l[11](null),z&&z.d(C),p=!1,_e(E)}}}function Dt(l,e,t){let r;Ce(l,xt,x=>t(5,r=x));let{$$slots:s={},$$scope:n}=e,{isOpen:a=!1}=e,{title:i}=e,f,c;const d=He(),g=()=>d("toggle",!0);function b(x){je[x?"unshift":"push"](()=>{c=x,t(3,c)})}const v=()=>d("toggle",!1);function k(x){je[x?"unshift":"push"](()=>{f=x,t(2,f)})}return l.$$set=x=>{"isOpen"in x&&t(1,a=x.isOpen),"title"in x&&t(0,i=x.title),"$$scope"in x&&t(6,n=x.$$scope)},l.$$.update=()=>{l.$$.dirty&1&&t(0,i=i||"New Chat"),l.$$.dirty&32&&r&&d("toggle",!1),l.$$.dirty&14&&(a&&f?f.focus():!a&&Mt&&document.activeElement===f&&c.focus())},[i,a,f,c,d,r,n,s,g,b,v,k]}class jt extends de{constructor(e){super(),he(this,e,Dt,Bt,fe,{isOpen:1,title:0})}}function Pt(){const{classList:l}=document.querySelector("html"),e=document.querySelector('meta[name="theme-color"]');l.contains("dark")?(l.remove("dark"),e.setAttribute("content","rgb(249, 250, 251)"),localStorage.theme="light"):(l.add("dark"),e.setAttribute("content","rgb(26, 36, 50)"),localStorage.theme="dark")}function Vt(l){let e,t,r='<path fill="currentColor" d="m13 24l-9-9l1.414-1.414L13 21.171L26.586 7.586L28 9z"/>',s=[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},l[0]],n={};for(let a=0;a<s.length;a+=1)n=ie(n,s[a]);return{c(){e=ce("svg"),t=new Se(!0),this.h()},l(a){e=ue(a,"svg",{viewBox:!0,width:!0,height:!0});var i=A(e);t=Ne(i,!0),i.forEach(u),this.h()},h(){t.a=null,ve(e,n)},m(a,i){H(a,e,i),t.m(r,e)},p(a,[i]){ve(e,n=Oe(s,[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},i&1&&a[0]]))},i:ne,o:ne,d(a){a&&u(e)}}}function zt(l,e,t){return l.$$set=r=>{t(0,e=ie(ie({},e),pe(r)))},e=pe(e),[e]}class Ut extends de{constructor(e){super(),he(this,e,zt,Vt,fe,{})}}function Gt(l){let e,t,r='<path fill="currentColor" d="M12 12h2v12h-2zm6 0h2v12h-2z"/><path fill="currentColor" d="M4 6v2h2v20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8h2V6zm4 22V8h16v20zm4-26h8v2h-8z"/>',s=[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},l[0]],n={};for(let a=0;a<s.length;a+=1)n=ie(n,s[a]);return{c(){e=ce("svg"),t=new Se(!0),this.h()},l(a){e=ue(a,"svg",{viewBox:!0,width:!0,height:!0});var i=A(e);t=Ne(i,!0),i.forEach(u),this.h()},h(){t.a=null,ve(e,n)},m(a,i){H(a,e,i),t.m(r,e)},p(a,[i]){ve(e,n=Oe(s,[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},i&1&&a[0]]))},i:ne,o:ne,d(a){a&&u(e)}}}function Ft(l,e,t){return l.$$set=r=>{t(0,e=ie(ie({},e),pe(r)))},e=pe(e),[e]}class Rt extends de{constructor(e){super(),he(this,e,Ft,Gt,fe,{})}}function Ze(l){let e,t="Delete";return{c(){e=w("span"),e.textContent=t,this.h()},l(r){e=y(r,"SPAN",{class:!0,"data-svelte-h":!0}),re(e)!=="svelte-1mt4bpd"&&(e.textContent=t),this.h()},h(){o(e,"class","font-semibold")},m(r,s){H(r,e,s)},d(r){r&&u(e)}}}function Zt(l){let e,t,r,s,n,a,i,f;return t=new $t({props:{class:"text-xs text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"}}),n=new Rt({props:{class:"text-xs text-gray-400  hover:text-gray-500 dark:hover:text-gray-300"}}),{c(){e=w("button"),J(t.$$.fragment),r=I(),s=w("button"),J(n.$$.fragment),this.h()},l(c){e=y(c,"BUTTON",{type:!0,class:!0,title:!0});var d=A(e);X(t.$$.fragment,d),d.forEach(u),r=B(c),s=y(c,"BUTTON",{type:!0,class:!0,title:!0});var g=A(s);X(n.$$.fragment,g),g.forEach(u),this.h()},h(){o(e,"type","button"),o(e,"class","flex h-5 w-5 items-center justify-center rounded md:hidden md:group-hover:flex"),o(e,"title","Edit conversation title"),o(s,"type","button"),o(s,"class","flex h-5 w-5 items-center justify-center rounded md:hidden md:group-hover:flex"),o(s,"title","Delete conversation")},m(c,d){H(c,e,d),Y(t,e,null),H(c,r,d),H(c,s,d),Y(n,s,null),a=!0,i||(f=[ae(e,"click",Ee(l[6])),ae(s,"click",Ee(l[7]))],i=!0)},p:ne,i(c){a||(j(t.$$.fragment,c),j(n.$$.fragment,c),a=!0)},o(c){V(t.$$.fragment,c),V(n.$$.fragment,c),a=!1},d(c){c&&(u(e),u(r),u(s)),ee(t),ee(n),i=!1,_e(f)}}}function qt(l){let e,t,r,s,n,a,i,f;return t=new Ut({props:{class:"text-xs text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"}}),n=new Ie({props:{class:"text-xs text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"}}),{c(){e=w("button"),J(t.$$.fragment),r=I(),s=w("button"),J(n.$$.fragment),this.h()},l(c){e=y(c,"BUTTON",{type:!0,class:!0,title:!0});var d=A(e);X(t.$$.fragment,d),d.forEach(u),r=B(c),s=y(c,"BUTTON",{type:!0,class:!0,title:!0});var g=A(s);X(n.$$.fragment,g),g.forEach(u),this.h()},h(){o(e,"type","button"),o(e,"class","flex h-5 w-5 items-center justify-center rounded md:hidden md:group-hover:flex"),o(e,"title","Confirm delete action"),o(s,"type","button"),o(s,"class","flex h-5 w-5 items-center justify-center rounded md:hidden md:group-hover:flex"),o(s,"title","Cancel delete action")},m(c,d){H(c,e,d),Y(t,e,null),H(c,r,d),H(c,s,d),Y(n,s,null),a=!0,i||(f=[ae(e,"click",Ee(l[4])),ae(s,"click",Ee(l[5]))],i=!0)},p:ne,i(c){a||(j(t.$$.fragment,c),j(n.$$.fragment,c),a=!0)},o(c){V(t.$$.fragment,c),V(n.$$.fragment,c),a=!1},d(c){c&&(u(e),u(r),u(s)),ee(t),ee(n),i=!1,_e(f)}}}function Kt(l){let e,t,r,s=l[0].title+"",n,a,i,f,c,d,g,b,v,k=l[1]&&Ze();const x=[qt,Zt],$=[];function M(p,E){return p[1]?0:1}return i=M(l),f=$[i]=x[i](l),{c(){e=w("a"),t=w("div"),k&&k.c(),r=I(),n=xe(s),a=I(),f.c(),this.h()},l(p){e=y(p,"A",{"data-sveltekit-noscroll":!0,href:!0,class:!0});var E=A(e);t=y(E,"DIV",{class:!0});var P=A(t);k&&k.l(P),r=B(P),n=$e(P,s),P.forEach(u),a=B(E),f.l(E),E.forEach(u),this.h()},h(){o(t,"class","flex-1 truncate"),o(e,"data-sveltekit-noscroll",""),o(e,"href",c=F+"/conversation/"+l[0].id),o(e,"class",d="group flex h-11 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 "+(l[0].id===l[2].params.id?"bg-gray-100 dark:bg-gray-700":""))},m(p,E){H(p,e,E),m(e,t),k&&k.m(t,null),m(t,r),m(t,n),m(e,a),$[i].m(e,null),g=!0,b||(v=[ae(e,"click",l[8]),ae(e,"mouseleave",l[9])],b=!0)},p(p,[E]){p[1]?k||(k=Ze(),k.c(),k.m(t,r)):k&&(k.d(1),k=null),(!g||E&1)&&s!==(s=p[0].title+"")&&Le(n,s);let P=i;i=M(p),i===P?$[i].p(p,E):(we(),V($[P],1,1,()=>{$[P]=null}),ye(),f=$[i],f?f.p(p,E):(f=$[i]=x[i](p),f.c()),j(f,1),f.m(e,null)),(!g||E&1&&c!==(c=F+"/conversation/"+p[0].id))&&o(e,"href",c),(!g||E&5&&d!==(d="group flex h-11 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 "+(p[0].id===p[2].params.id?"bg-gray-100 dark:bg-gray-700":"")))&&o(e,"class",d)},i(p){g||(j(f),g=!0)},o(p){V(f),g=!1},d(p){p&&u(e),k&&k.d(),$[i].d(),b=!1,_e(v)}}}function Qt(){let l="kg_id";const e=localStorage.getItem(l);try{const t=JSON.parse(e);Array.isArray(t)&&t.length>0&&(t.forEach(r=>{document.querySelectorAll(`.${r}`).forEach(s=>{s.innerHTML=""})}),localStorage.removeItem(l))}catch(t){console.error("Failed to parse localStorage array:",t)}}function Wt(l,e,t){let r;Ce(l,ut,v=>t(2,r=v));let{conv:s}=e,n=!1;const a=He(),i=()=>a("deleteConversation",s.id),f=()=>{t(1,n=!1)},c=()=>{const v=prompt("Edit this conversation title:",s.title);v&&a("editConversationTitle",{id:s.id,title:v})},d=v=>{v.shiftKey?a("deleteConversation",s.id):t(1,n=!0)},g=()=>Qt(),b=()=>{t(1,n=!1)};return l.$$set=v=>{"conv"in v&&t(0,s=v.conv)},[s,n,r,a,i,f,c,d,g,b]}class Jt extends de{constructor(e){super(),he(this,e,Wt,Kt,fe,{conv:0})}}function qe(l,e,t){const r=l.slice();return r[15]=e[t],r}function Xt(l){let e,t,r,s,n,a,i,f,c,d;return t=new ft({props:{classNames:"mr-1"}}),{c(){e=w("a"),J(t.$$.fragment),r=I(),s=w("h1"),n=w("img"),i=I(),f=w("img"),this.h()},l(g){e=y(g,"A",{class:!0,href:!0});var b=A(e);X(t.$$.fragment,b),r=B(b),s=y(b,"H1",{class:!0});var v=A(s);n=y(v,"IMG",{src:!0,class:!0,width:!0,alt:!0}),i=B(v),f=y(v,"IMG",{src:!0,class:!0,width:!0,alt:!0}),v.forEach(u),b.forEach(u),this.h()},h(){Ae(n.src,a=l[5])||o(n,"src",a),o(n,"class","block dark:hidden"),o(n,"width","150"),o(n,"alt","accure-logo"),Ae(f.src,c=l[6])||o(f,"src",c),o(f,"class","hidden dark:block"),o(f,"width","150"),o(f,"alt","accure-logo"),o(s,"class","text-1xl bg-gradient-to-r from-blue-600 via-indigo-500 to-purple-600 bg-clip-text font-medium tracking-wide text-transparent drop-shadow-lg"),o(e,"class","flex items-center rounded-xl text-lg font-semibold"),o(e,"href",ct+F+"/")},m(g,b){H(g,e,b),Y(t,e,null),m(e,r),m(e,s),m(s,n),m(s,i),m(s,f),d=!0},p:ne,i(g){d||(j(t.$$.fragment,g),d=!0)},o(g){V(t.$$.fragment,g),d=!1},d(g){g&&u(e),ee(t)}}}function Yt(l){let e,t,r,s,n;return t=new ft({props:{classNames:"mr-1"}}),{c(){e=w("a"),J(t.$$.fragment),r=I(),s=xe(l[4]),this.h()},l(a){e=y(a,"A",{class:!0,href:!0});var i=A(e);X(t.$$.fragment,i),r=B(i),s=$e(i,l[4]),i.forEach(u),this.h()},h(){o(e,"class","flex items-center rounded-xl text-lg font-semibold"),o(e,"href",ct+F+"/")},m(a,i){H(a,e,i),Y(t,e,null),m(e,r),m(e,s),n=!0},p(a,i){(!n||i&16)&&Le(s,a[4])},i(a){n||(j(t.$$.fragment,a),n=!0)},o(a){V(t.$$.fragment,a),n=!1},d(a){a&&u(e),ee(t)}}}function Ke(l){let e,t;return e=new Jt({props:{conv:l[15]}}),e.$on("editConversationTitle",l[8]),e.$on("deleteConversation",l[9]),{c(){J(e.$$.fragment)},l(r){X(e.$$.fragment,r)},m(r,s){Y(e,r,s),t=!0},p(r,s){const n={};s&1&&(n.conv=r[15]),e.$set(n)},i(r){t||(j(e.$$.fragment,r),t=!0)},o(r){V(e.$$.fragment,r),t=!1},d(r){ee(e,r)}}}function Qe(l,e){let t,r,s,n=e[15].title!=="New Chat"&&Ke(e);return{key:l,first:null,c(){t=Me(),n&&n.c(),r=Me(),this.h()},l(a){t=Me(),n&&n.l(a),r=Me(),this.h()},h(){this.first=t},m(a,i){H(a,t,i),n&&n.m(a,i),H(a,r,i),s=!0},p(a,i){e=a,e[15].title!=="New Chat"?n?(n.p(e,i),i&1&&j(n,1)):(n=Ke(e),n.c(),j(n,1),n.m(r.parentNode,r)):n&&(we(),V(n,1,1,()=>{n=null}),ye())},i(a){s||(j(n),s=!0)},o(a){V(n),s=!1},d(a){a&&(u(t),u(r)),n&&n.d(a)}}}function We(l){var P,z,C;let e,t,r,s=(((P=l[2])==null?void 0:P.username)||((z=l[2])==null?void 0:z.email))+"",n,a,i,f=`<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m4 4v-4m0-4v4m0 4v-4"></path></svg>
				Sign Out`,c,d,g=`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" class="h-5 w-5 text-gray-700 dark:text-gray-300"><path stroke-linecap="round" stroke-linejoin="round" d="M4.5 6.75C4.5 5.231 7.186 4 10.5 4s6 1.231 6 2.75v.5c0 1.519-2.686 2.75-6 2.75s-6-1.231-6-2.75v-.5zM16.5 9.75v3c0 1.519-2.686 2.75-6 2.75s-6-1.231-6-2.75v-3m12 3v3c0 1.519-2.686 2.75-6 2.75s-6-1.231-6-2.75v-3"></path></svg>

			Data Sources`,b,v,k,x=`<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v6m0 0v6m0-6h12m-6-6v6m0 0v6"></path></svg>
			Admin`,$,M,p=((C=l[2])==null?void 0:C.isSubscriptionEnabled)&&Je(),E=er();return{c(){p&&p.c(),e=I(),t=w("form"),r=w("span"),n=xe(s),a=I(),i=w("button"),i.innerHTML=f,c=I(),d=w("a"),d.innerHTML=g,b=I(),E&&E.c(),v=I(),k=w("a"),k.innerHTML=x,this.h()},l(_){p&&p.l(_),e=B(_),t=y(_,"FORM",{action:!0,method:!0,class:!0});var D=A(t);r=y(D,"SPAN",{class:!0});var S=A(r);n=$e(S,s),S.forEach(u),a=B(D),i=y(D,"BUTTON",{type:!0,class:!0,"data-svelte-h":!0}),re(i)!=="svelte-z3e9wx"&&(i.innerHTML=f),D.forEach(u),c=B(_),d=y(_,"A",{href:!0,class:!0,"data-svelte-h":!0}),re(d)!=="svelte-wd6mb8"&&(d.innerHTML=g),b=B(_),E&&E.l(_),v=B(_),k=y(_,"A",{href:!0,class:!0,"data-svelte-h":!0}),re(k)!=="svelte-1s7jw19"&&(k.innerHTML=x),this.h()},h(){o(r,"class","flex h-9 flex-none shrink items-center gap-1.5 truncate pr-2 text-gray-500 dark:text-gray-400"),o(i,"type","submit"),o(i,"class","ml-auto h-6 flex-none items-center gap-1.5 rounded-md border bg-white px-2 text-gray-700 shadow-sm hover:shadow-none group-hover:flex dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400 dark:hover:text-gray-300 md:hidden"),o(t,"action",F+"/logout"),o(t,"method","post"),o(t,"class","group flex items-center gap-1.5 rounded-lg pl-3 pr-2 hover:bg-gray-100 dark:hover:bg-gray-700"),o(d,"href",F+"/docs"),o(d,"class","flex h-9 flex-none items-center gap-1.5 rounded-lg bg-gray-100 pl-3 pr-2 text-gray-500 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600"),o(k,"href",F+"/admin/users"),o(k,"class","flex h-9 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700")},m(_,D){p&&p.m(_,D),H(_,e,D),H(_,t,D),m(t,r),m(r,n),m(t,a),m(t,i),H(_,c,D),H(_,d,D),H(_,b,D),E&&E.m(_,D),H(_,v,D),H(_,k,D),$||(M=ae(i,"click",l[10]),$=!0)},p(_,D){var S,U,O;(S=_[2])!=null&&S.isSubscriptionEnabled?p||(p=Je(),p.c(),p.m(e.parentNode,e)):p&&(p.d(1),p=null),D&4&&s!==(s=(((U=_[2])==null?void 0:U.username)||((O=_[2])==null?void 0:O.email))+"")&&Le(n,s)},d(_){_&&(u(e),u(t),u(c),u(d),u(b),u(v),u(k)),p&&p.d(_),E&&E.d(_),$=!1,M()}}}function Je(l){return{c:ne,l:ne,m:ne,d:ne}}function er(l){let e,t=`<svg width="22" height="22" viewBox="0 0 64 64" fill="white" xmlns="http://www.w3.org/2000/svg"><path d="M32 2L44 9V23L32 30L20 23V9L32 2Z" stroke="black" stroke-width="2" fill="white"></path><path d="M44 23L56 30V44L44 51L32 44V30L44 23Z" stroke="black" stroke-width="2" fill="white"></path><path d="M20 23L32 30V44L20 51L8 44V30L20 23Z" stroke="black" stroke-width="2" fill="white"></path></svg>
			Agentic AI`;return{c(){e=w("a"),e.innerHTML=t,this.h()},l(r){e=y(r,"A",{href:!0,class:!0,"data-svelte-h":!0}),re(e)!=="svelte-1617utm"&&(e.innerHTML=t),this.h()},h(){o(e,"href",F+"/agentic_workflow"),o(e,"class","flex h-9 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700")},m(r,s){H(r,e,s)},d(r){r&&u(e)}}}function Xe(l){let e,t=`<button type="submit" class="flex h-9 w-full flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"><svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12H3m0 0l4-4m-4 4l4 4m0-6v12m6-6l6 6m-6-6l6-6"></path></svg>
				Login</button>`;return{c(){e=w("form"),e.innerHTML=t,this.h()},l(r){e=y(r,"FORM",{action:!0,method:!0,target:!0,"data-svelte-h":!0}),re(e)!=="svelte-1gxtioi"&&(e.innerHTML=t),this.h()},h(){o(e,"action",F+"/login"),o(e,"method","POST"),o(e,"target","_parent")},m(r,s){H(r,e,s)},d(r){r&&u(e)}}}function tr(l){var L,N;let e,t,r,s,n,a="New Chat",i,f,c,d=[],g=new Map,b,v,k,x,$,M=`<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v18m9-9H3m18 0l-9 9m0-18l9 9"></path></svg>
		Theme`,p,E,P=`<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8.5v7m-5 1l1-1m12 0l1 1m-7-1v7m-5-5l-1-1m12 0l-1 1"></path></svg>
		Settings`,z,C,_=`<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
			Help`,D,S,U=`<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6m18 0l-6 6m0-12l6 6"></path></svg>
			Terms &amp; Privacy`,O,te,se;const le=[Yt,Xt],q=[];function K(T,h){return 1}t=K(),r=q[t]=le[t](l);let R=Re(l[0]);const ge=T=>T[15].id;for(let T=0;T<R.length;T+=1){let h=qe(l,R,T),Z=ge(h);g.set(Z,d[T]=Qe(Z,h))}let G=(((L=l[2])==null?void 0:L.username)||((N=l[2])==null?void 0:N.email))&&We(l),W=l[1]&&Xe();return{c(){e=w("div"),r.c(),s=I(),n=w("a"),n.textContent=a,i=I(),f=I(),c=w("div");for(let T=0;T<d.length;T+=1)d[T].c();b=I(),v=w("div"),G&&G.c(),k=I(),W&&W.c(),x=I(),$=w("button"),$.innerHTML=M,p=I(),E=w("button"),E.innerHTML=P,z=I(),C=w("a"),C.innerHTML=_,D=I(),S=w("a"),S.innerHTML=U,this.h()},l(T){e=y(T,"DIV",{class:!0});var h=A(e);r.l(h),s=B(h),n=y(h,"A",{href:!0,class:!0,"data-svelte-h":!0}),re(n)!=="svelte-111q0zn"&&(n.textContent=a),h.forEach(u),i=B(T),f=B(T),c=y(T,"DIV",{class:!0});var Z=A(c);for(let Te=0;Te<d.length;Te+=1)d[Te].l(Z);Z.forEach(u),b=B(T),v=y(T,"DIV",{class:!0});var Q=A(v);G&&G.l(Q),k=B(Q),W&&W.l(Q),x=B(Q),$=y(Q,"BUTTON",{type:!0,class:!0,"data-svelte-h":!0}),re($)!=="svelte-vtzte8"&&($.innerHTML=M),p=B(Q),E=y(Q,"BUTTON",{type:!0,class:!0,"data-svelte-h":!0}),re(E)!=="svelte-186fm8t"&&(E.innerHTML=P),z=B(Q),C=y(Q,"A",{href:!0,class:!0,"data-svelte-h":!0}),re(C)!=="svelte-gzxl0x"&&(C.innerHTML=_),D=B(Q),S=y(Q,"A",{href:!0,class:!0,"data-svelte-h":!0}),re(S)!=="svelte-fuwst5"&&(S.innerHTML=U),Q.forEach(u),this.h()},h(){o(n,"href",`${F}/`),o(n,"class","flex rounded-lg border bg-white px-2 py-0.5 text-center shadow-sm hover:shadow-none dark:border-gray-600 dark:bg-gray-700"),o(e,"class","brand-name sticky top-0 flex flex-none items-center justify-between px-3 py-3.5 pb-0 max-sm:pt-0"),o(c,"class","scrollbar-custom flex flex-col gap-1 overflow-y-auto rounded-r-xl bg-gradient-to-l from-gray-50 px-3 pb-3 pt-2 dark:from-gray-800/30"),o($,"type","button"),o($,"class","flex h-9 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),o(E,"type","button"),o(E,"class","flex h-9 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),o(C,"href",F+"/help"),o(C,"class","flex h-9 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),o(S,"href",F+"/privacy"),o(S,"class","flex h-9 flex-none items-center gap-1.5 rounded-lg pl-3 pr-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),o(v,"class","mt-0.5 flex flex-col gap-1 rounded-r-xl bg-gradient-to-l from-gray-50 p-3 text-sm dark:from-gray-800/30")},m(T,h){H(T,e,h),q[t].m(e,null),m(e,s),m(e,n),H(T,i,h),H(T,f,h),H(T,c,h);for(let Z=0;Z<d.length;Z+=1)d[Z]&&d[Z].m(c,null);H(T,b,h),H(T,v,h),G&&G.m(v,null),m(v,k),W&&W.m(v,null),m(v,x),m(v,$),m(v,p),m(v,E),m(v,z),m(v,C),m(v,D),m(v,S),O=!0,te||(se=[ae($,"click",Pt),ae(E,"click",l[11])],te=!0)},p(T,[h]){var Z,Q;r.p(T,h),h&1&&(R=Re(T[0]),we(),d=Ct(d,h,ge,1,T,R,g,c,Et,Qe,null,qe),ye()),(Z=T[2])!=null&&Z.username||(Q=T[2])!=null&&Q.email?G?G.p(T,h):(G=We(T),G.c(),G.m(v,k)):G&&(G.d(1),G=null),T[1]?W||(W=Xe(),W.c(),W.m(v,x)):W&&(W.d(1),W=null)},i(T){if(!O){j(r);for(let h=0;h<R.length;h+=1)j(d[h]);O=!0}},o(T){V(r);for(let h=0;h<d.length;h+=1)V(d[h]);O=!1},d(T){T&&(u(e),u(i),u(f),u(c),u(b),u(v)),q[t].d();for(let h=0;h<d.length;h+=1)d[h].d();G&&G.d(),W&&W.d(),te=!1,_e(se)}}}function rr(l,e,t){const r="/images/AccureIQLight.png",s="/images/AccureIQDark.png",n=He();let{conversations:a=[]}=e,{canLogin:i}=e,{user:f}=e,c="",d=it;at(()=>{c=window.location.hostname,c.includes("securegpt.accure.ai")?t(4,d="SecureGPT"):c.includes("legalgpt.accure.ai")&&t(4,d="LegalGPT")});function g(){Tt.set(""),localStorage.removeItem("assistantName")}function b($){Be.call(this,l,$)}function v($){Be.call(this,l,$)}const k=()=>g(),x=()=>n("clickSettings");return l.$$set=$=>{"conversations"in $&&t(0,a=$.conversations),"canLogin"in $&&t(1,i=$.canLogin),"user"in $&&t(2,f=$.user)},[a,i,f,g,d,r,s,n,b,v,k,x]}class mt extends de{constructor(e){super(),he(this,e,rr,tr,fe,{conversations:0,canLogin:1,user:2,clearAssistantName:3})}get clearAssistantName(){return this.$$.ctx[3]}}function nr(l){let e,t,r,s,n,a,i;return{c(){e=ce("svg"),t=ce("path"),r=ce("path"),s=ce("defs"),n=ce("radialGradient"),a=ce("stop"),i=ce("stop"),this.h()},l(f){e=ue(f,"svg",{xmlns:!0,width:!0,height:!0,class:!0,fill:!0,viewBox:!0});var c=A(e);t=ue(c,"path",{fill:!0,d:!0}),A(t).forEach(u),r=ue(c,"path",{fill:!0,"fill-rule":!0,d:!0,"clip-rule":!0}),A(r).forEach(u),s=ue(c,"defs",{});var d=A(s);n=ue(d,"radialGradient",{id:!0,cx:!0,cy:!0,r:!0,gradientTransform:!0,gradientUnits:!0});var g=A(n);a=ue(g,"stop",{"stop-color":!0}),A(a).forEach(u),i=ue(g,"stop",{offset:!0,"stop-color":!0}),A(i).forEach(u),g.forEach(u),d.forEach(u),c.forEach(u),this.h()},h(){o(t,"fill","url(#a)"),o(t,"d","M.93 10.65A10.17 10.17 0 0 1 11.11.48h4.67a9.45 9.45 0 0 1 0 18.89H4.53L1.62 22.2a.38.38 0 0 1-.69-.28V10.65Z"),o(r,"fill","#000"),o(r,"fill-rule","evenodd"),o(r,"d","M11.52 7.4a1.86 1.86 0 1 1-3.72 0 1.86 1.86 0 0 1 3.72 0Zm7.57 0a1.86 1.86 0 1 1-3.73 0 1.86 1.86 0 0 1 3.73 0ZM8.9 12.9a.55.55 0 0 0-.*********** 0 0 1-1.51 0c0-.95.67-1.94 1.76-1.94 1.09 0 1.76 1 1.76 1.94H9.3a.55.55 0 0 0-.12-.35c-.06-.07-.1-.08-.13-.08s-.08 0-.14.08Zm4.04 0a.55.55 0 0 0-.12.35h-1.51c0-.95.68-1.94 1.76-1.94 1.1 0 1.77 1 1.77 1.94h-1.51a.55.55 0 0 0-.12-.35c-.06-.07-.11-.08-.14-.08-.02 0-.07 0-.13.08Zm-1.89.79c-.02 0-.07-.01-.13-.08a.55.55 0 0 1-.12-.36h-1.5c0 .95.67 1.95 1.75 1.95 1.1 0 1.77-1 1.77-1.95h-1.51c0 .16-.06.28-.12.36-.06.07-.11.08-.14.08Zm4.04 0c-.03 0-.08-.01-.14-.08a.55.55 0 0 1-.12-.36h-1.5c0 .95.67 1.95 1.76 1.95 1.08 0 1.76-1 1.76-1.95h-1.51c0 .16-.06.28-.12.36-.06.07-.11.08-.13.08Zm1.76-.44c0-.16.05-.28.12-.35.06-.07.1-.08.13-.08s.08 0 .14.08c.**********.11.35a.76.76 0 0 0 1.51 0c0-.95-.67-1.94-1.76-1.94-1.09 0-1.76 1-1.76 1.94h1.5Z"),o(r,"clip-rule","evenodd"),o(a,"stop-color","#FFD21E"),o(i,"offset","1"),o(i,"stop-color","red"),o(n,"id","a"),o(n,"cx","0"),o(n,"cy","0"),o(n,"r","1"),o(n,"gradientTransform","matrix(0 31.37 -34.85 0 13.08 -9.02)"),o(n,"gradientUnits","userSpaceOnUse"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"width","1em"),o(e,"height","1em"),o(e,"class",l[0]),o(e,"fill","none"),o(e,"viewBox","0 0 26 23")},m(f,c){H(f,e,c),m(e,t),m(e,r),m(e,s),m(s,n),m(n,a),m(n,i)},p(f,[c]){c&1&&o(e,"class",f[0])},i:ne,o:ne,d(f){f&&u(e)}}}function lr(l,e,t){let{classNames:r=""}=e;return l.$$set=s=>{"classNames"in s&&t(0,r=s.classNames)},[r]}class sr extends de{constructor(e){super(),he(this,e,lr,nr,fe,{classNames:0})}}function ar(l){let e,t,r,s,n,a,i,f;return r=new sr({props:{classNames:"text-2xl mr-2"}}),{c(){e=w("div"),t=w("div"),J(r.$$.fragment),s=I(),n=w("h2"),a=xe(l[0]),this.h()},l(c){e=y(c,"DIV",{class:!0});var d=A(e);t=y(d,"DIV",{class:!0});var g=A(t);X(r.$$.fragment,g),s=B(g),n=y(g,"H2",{class:!0});var b=A(n);a=$e(b,l[0]),b.forEach(u),g.forEach(u),d.forEach(u),this.h()},h(){o(n,"class","font-semibold"),o(t,"class","pointer-events-auto flex items-center rounded-full bg-white/90 px-3 py-1 shadow-sm dark:bg-gray-900/80"),o(e,"class","pointer-events-none fixed right-0 top-12 z-20 bg-gradient-to-bl from-red-500/20 via-red-500/0 to-red-500/0 pb-36 pl-36 pr-2 pt-2 md:top-0 md:pr-8 md:pt-5")},m(c,d){H(c,e,d),m(e,t),Y(r,t,null),m(t,s),m(t,n),m(n,a),f=!0},p(c,[d]){(!f||d&1)&&Le(a,c[0])},i(c){f||(j(r.$$.fragment,c),vt(()=>{f&&(i||(i=Pe(e,Fe,{duration:300},!0)),i.run(1))}),f=!0)},o(c){V(r.$$.fragment,c),i||(i=Pe(e,Fe,{duration:300},!1)),i.run(0),f=!1},d(c){c&&u(e),ee(r),c&&i&&i.end()}}}function or(l,e,t){let{message:r=""}=e;return l.$$set=s=>{"message"in s&&t(0,r=s.message)},[r]}class ir extends de{constructor(e){super(),he(this,e,or,ar,fe,{message:0})}}function Ye(l){let e,t;return e=new dt({props:{$$slots:{default:[cr]},$$scope:{ctx:l}}}),e.$on("close",l[12]),{c(){J(e.$$.fragment)},l(r){X(e.$$.fragment,r)},m(r,s){Y(e,r,s),t=!0},p(r,s){const n={};s&2097160&&(n.$$scope={dirty:s,ctx:r}),e.$set(n)},i(r){t||(j(e.$$.fragment,r),t=!0)},o(r){V(e.$$.fragment,r),t=!1},d(r){ee(e,r)}}}function cr(l){let e,t,r,s="Are you sure?",n,a,i,f,c,d="This action will delete all your conversations. This cannot be undone.",g,b,v="Confirm deletion",k,x,$;return i=new Ie({props:{class:"text-gray-900 group-hover:text-gray-500"}}),{c(){e=w("form"),t=w("div"),r=w("h2"),r.textContent=s,n=I(),a=w("button"),J(i.$$.fragment),f=I(),c=w("p"),c.textContent=d,g=I(),b=w("button"),b.textContent=v,this.h()},l(M){e=y(M,"FORM",{method:!0,action:!0,class:!0});var p=A(e);t=y(p,"DIV",{class:!0});var E=A(t);r=y(E,"H2",{"data-svelte-h":!0}),re(r)!=="svelte-1xhqmfb"&&(r.textContent=s),n=B(E),a=y(E,"BUTTON",{type:!0,class:!0});var P=A(a);X(i.$$.fragment,P),P.forEach(u),E.forEach(u),f=B(p),c=y(p,"P",{class:!0,"data-svelte-h":!0}),re(c)!=="svelte-tlhizs"&&(c.textContent=d),g=B(p),b=y(p,"BUTTON",{type:!0,class:!0,"data-svelte-h":!0}),re(b)!=="svelte-1r34sqw"&&(b.textContent=v),p.forEach(u),this.h()},h(){o(a,"type","button"),o(a,"class","group"),o(t,"class","flex items-start justify-between text-xl font-semibold text-gray-800"),o(c,"class","text-gray-800"),o(b,"type","submit"),o(b,"class","mt-2 rounded-full bg-red-700 px-5 py-2 text-lg font-semibold text-gray-100 ring-gray-400 ring-offset-1 transition-all focus-visible:outline-none focus-visible:ring hover:ring"),o(e,"method","post"),o(e,"action",F+"/conversations?/delete"),o(e,"class","flex w-full flex-col gap-5 p-6")},m(M,p){H(M,e,p),m(e,t),m(t,r),m(t,n),m(t,a),Y(i,a,null),m(e,f),m(e,c),m(e,g),m(e,b),k=!0,x||($=[ae(a,"click",l[10]),ot(ht.call(null,e,l[11]))],x=!0)},p:ne,i(M){k||(j(i.$$.fragment,M),k=!0)},o(M){V(i.$$.fragment,M),k=!1},d(M){M&&u(e),ee(i),x=!1,_e($)}}}function ur(l){let e,t,r,s="Settings",n,a,i,f,c,d,g,b,v,k,x,$='<button type="submit" class="underline decoration-gray-300 hover:decoration-gray-700">Delete all conversations</button>',M,p,E="Apply",P,z,C,_;i=new Ie({props:{class:"text-gray-900 group-hover:text-gray-500"}});let D=bt,S=l[3]&&Ye(l);return{c(){e=w("div"),t=w("div"),r=w("h2"),r.textContent=s,n=I(),a=w("button"),J(i.$$.fragment),f=I(),c=w("form"),d=I(),g=w("label"),b=w("input"),v=xe(`
				Hide emoticons in conversation topics`),k=I(),x=w("form"),x.innerHTML=$,M=I(),p=w("button"),p.textContent=E,P=I(),S&&S.c(),this.h()},l(U){e=y(U,"DIV",{class:!0});var O=A(e);t=y(O,"DIV",{class:!0});var te=A(t);r=y(te,"H2",{"data-svelte-h":!0}),re(r)!=="svelte-15f2bar"&&(r.textContent=s),n=B(te),a=y(te,"BUTTON",{type:!0,class:!0});var se=A(a);X(i.$$.fragment,se),se.forEach(u),te.forEach(u),f=B(O),c=y(O,"FORM",{class:!0,method:!0,action:!0});var le=A(c);d=B(le),g=y(le,"LABEL",{class:!0});var q=A(g);b=y(q,"INPUT",{type:!0,name:!0}),v=$e(q,`
				Hide emoticons in conversation topics`),q.forEach(u),k=B(le),x=y(le,"FORM",{method:!0,action:!0,"data-svelte-h":!0}),re(x)!=="svelte-1ldioy7"&&(x.innerHTML=$),M=B(le),p=y(le,"BUTTON",{type:!0,class:!0,"data-svelte-h":!0}),re(p)!=="svelte-1jwaby2"&&(p.textContent=E),le.forEach(u),P=B(O),S&&S.l(O),O.forEach(u),this.h()},h(){o(a,"type","button"),o(a,"class","group"),o(t,"class","flex items-start justify-between text-xl font-semibold text-gray-800"),o(b,"type","checkbox"),o(b,"name","hideEmojiOnSidebar"),o(g,"class","flex cursor-pointer select-none items-center gap-2 text-sm text-gray-500"),o(x,"method","post"),o(x,"action",F+"/conversations?/delete"),o(p,"type","submit"),o(p,"class","mt-2 rounded-full bg-black px-5 py-2 text-lg font-semibold text-gray-100 ring-gray-400 ring-offset-1 transition-all focus-visible:outline-none focus-visible:ring hover:ring"),o(c,"class","flex flex-col gap-5"),o(c,"method","post"),o(c,"action",F+"/settings"),o(e,"class","flex w-full flex-col gap-5 p-6")},m(U,O){H(U,e,O),m(e,t),m(t,r),m(t,n),m(t,a),Y(i,a,null),m(e,f),m(e,c),m(c,d),m(c,g),m(g,b),b.checked=l[0].hideEmojiOnSidebar,m(g,v),m(c,k),m(c,x),m(c,M),m(c,p),m(e,P),S&&S.m(e,null),z=!0,C||(_=[ae(a,"click",l[5]),ae(b,"change",l[7]),ae(x,"submit",Ee(l[8])),ot(ht.call(null,c,l[9]))],C=!0)},p(U,O){O&1&&(b.checked=U[0].hideEmojiOnSidebar),U[3]?S?(S.p(U,O),O&8&&j(S,1)):(S=Ye(U),S.c(),j(S,1),S.m(e,null)):S&&(we(),V(S,1,1,()=>{S=null}),ye())},i(U){z||(j(i.$$.fragment,U),j(D),j(S),z=!0)},o(U){V(i.$$.fragment,U),V(D),V(S),z=!1},d(U){U&&u(e),ee(i),S&&S.d(),C=!1,_e(_)}}}function fr(l){let e,t;return e=new dt({props:{$$slots:{default:[ur]},$$scope:{ctx:l}}}),e.$on("close",l[13]),{c(){J(e.$$.fragment)},l(r){X(e.$$.fragment,r)},m(r,s){Y(e,r,s),t=!0},p(r,[s]){const n={};s&2097167&&(n.$$scope={dirty:s,ctx:r}),e.$set(n)},i(r){t||(j(e.$$.fragment,r),t=!0)},o(r){V(e.$$.fragment,r),t=!1},d(r){ee(e,r)}}}function dr(l,e,t){let{settings:r}=e,{models:s}=e,n=r.shareConversationsWithModelAuthors,a=!1;const i=He(),f=()=>i("close");function c(M){n=M,t(2,n)}function d(){r.hideEmojiOnSidebar=this.checked,t(0,r)}const g=()=>t(3,a=!0),b=()=>{i("close")},v=()=>t(3,a=!1),k=()=>{i("close")},x=()=>t(3,a=!1);function $(M){Be.call(this,l,M)}return l.$$set=M=>{"settings"in M&&t(0,r=M.settings),"models"in M&&t(1,s=M.models)},[r,s,n,a,i,f,c,d,g,b,v,k,x,$]}class hr extends de{constructor(e){super(),he(this,e,dr,fr,fe,{settings:0,models:1})}}const{document:oe}=kt;function mr(l){let e,t,r;return{c(){e=w("div"),t=w("img"),this.h()},l(s){e=y(s,"DIV",{class:!0});var n=A(e);t=y(n,"IMG",{src:!0,width:!0,alt:!0}),n.forEach(u),this.h()},h(){Ae(t.src,r=l[5]?`${l[8]}`:`${l[7]}`)||o(t,"src",r),o(t,"width","150"),o(t,"alt","accure-logo"),o(e,"class","accure-logo-mobile")},m(s,n){H(s,e,n),m(e,t)},p(s,n){n[0]&32&&!Ae(t.src,r=s[5]?`${s[8]}`:`${s[7]}`)&&o(t,"src",r)},d(s){s&&u(e)}}}function gr(l){let e,t,r,s=l[9]==="true"&&mr(l);return t=new mt({props:{conversations:l[0].conversations,user:l[0].user,canLogin:l[0].user===void 0&&l[0].loginEnabled}}),t.$on("shareConversation",l[15]),t.$on("deleteConversation",l[16]),t.$on("clickSettings",l[17]),t.$on("editConversationTitle",l[18]),{c(){s&&s.c(),e=I(),J(t.$$.fragment)},l(n){s&&s.l(n),e=B(n),X(t.$$.fragment,n)},m(n,a){s&&s.m(n,a),H(n,e,a),Y(t,n,a),r=!0},p(n,a){n[9]==="true"&&s.p(n,a);const i={};a[0]&1&&(i.conversations=n[0].conversations),a[0]&1&&(i.user=n[0].user),a[0]&1&&(i.canLogin=n[0].user===void 0&&n[0].loginEnabled),t.$set(i)},i(n){r||(j(t.$$.fragment,n),r=!0)},o(n){V(t.$$.fragment,n),r=!1},d(n){n&&u(e),s&&s.d(n),ee(t,n)}}}function et(l){let e,t;return e=new ir({props:{message:l[3]}}),{c(){J(e.$$.fragment)},l(r){X(e.$$.fragment,r)},m(r,s){Y(e,r,s),t=!0},p(r,s){const n={};s[0]&8&&(n.message=r[3]),e.$set(n)},i(r){t||(j(e.$$.fragment,r),t=!0)},o(r){V(e.$$.fragment,r),t=!1},d(r){ee(e,r)}}}function tt(l){let e,t;return e=new hr({props:{settings:l[0].settings,models:l[0].models}}),e.$on("close",l[25]),{c(){J(e.$$.fragment)},l(r){X(e.$$.fragment,r)},m(r,s){Y(e,r,s),t=!0},p(r,s){const n={};s[0]&1&&(n.settings=r[0].settings),s[0]&1&&(n.models=r[0].models),e.$set(n)},i(r){t||(j(e.$$.fragment,r),t=!0)},o(r){V(e.$$.fragment,r),t=!1},d(r){ee(e,r)}}}function vr(l){var W;let e,t="",r,s,n,a,i,f,c,d,g,b,v,k,x,$,M,p,E,P,z,C,_,D,S,U,O,te,se,le,q;oe.title=s=l[4],D=new jt({props:{isOpen:l[1],title:(W=l[0].conversations.find(l[19]))==null?void 0:W.title,$$slots:{default:[gr]},$$scope:{ctx:l}}}),D.$on("toggle",l[20]),O=new mt({props:{conversations:l[0].conversations,user:l[0].user,canLogin:l[0].user===void 0&&l[0].loginEnabled}}),O.$on("shareConversation",l[21]),O.$on("deleteConversation",l[22]),O.$on("clickSettings",l[23]),O.$on("editConversationTitle",l[24]);let K=l[3]&&et(l),R=l[2]&&tt(l);const ge=l[14].default,G=rt(ge,l,l[26],null);return{c(){e=w("form"),e.innerHTML=t,r=I(),n=w("meta"),a=w("meta"),i=w("meta"),f=w("meta"),c=w("meta"),d=w("meta"),b=w("meta"),k=w("link"),$=w("link"),p=w("link"),P=w("link"),C=I(),_=w("div"),J(D.$$.fragment),S=I(),U=w("nav"),J(O.$$.fragment),te=I(),K&&K.c(),se=I(),R&&R.c(),le=I(),G&&G.c(),this.h()},l(L){e=y(L,"FORM",{id:!0,action:!0,method:!0,"data-svelte-h":!0}),re(e)!=="svelte-17l3g37"&&(e.innerHTML=t),r=B(L);const N=pt("svelte-1j0gxs0",oe.head);n=y(N,"META",{name:!0,content:!0}),a=y(N,"META",{name:!0,content:!0}),i=y(N,"META",{name:!0,content:!0}),f=y(N,"META",{property:!0,content:!0}),c=y(N,"META",{property:!0,content:!0}),d=y(N,"META",{property:!0,content:!0}),b=y(N,"META",{property:!0,content:!0}),k=y(N,"LINK",{rel:!0,href:!0,sizes:!0}),$=y(N,"LINK",{rel:!0,href:!0,type:!0}),p=y(N,"LINK",{rel:!0,href:!0}),P=y(N,"LINK",{rel:!0,href:!0}),N.forEach(u),C=B(L),_=y(L,"DIV",{class:!0});var T=A(_);X(D.$$.fragment,T),S=B(T),U=y(T,"NAV",{class:!0});var h=A(U);X(O.$$.fragment,h),h.forEach(u),te=B(T),K&&K.l(T),se=B(T),R&&R.l(T),le=B(T),G&&G.l(T),T.forEach(u),this.h()},h(){o(e,"id","logoutForm"),o(e,"action","/logout"),o(e,"method","post"),o(n,"name","description"),o(n,"content","SecureGPT for private data. 💪"),o(a,"name","twitter:card"),o(a,"content","summary_large_image"),o(i,"name","twitter:site"),o(i,"content","@huggingface"),o(f,"property","og:title"),o(f,"content",l[4]),o(c,"property","og:type"),o(c,"content","website"),o(d,"property","og:url"),o(d,"content",g=""+(l[6].url.origin+F)),o(b,"property","og:image"),o(b,"content",v=l[6].url.origin+F+"/"+me+"/thumbnail.png"),o(k,"rel","icon"),o(k,"href",x=l[6].url.origin+F+"/"+me+"/favicon.ico"),o(k,"sizes","32x32"),o($,"rel","icon"),o($,"href",M=l[6].url.origin+F+"/"+me+"/icon.svg"),o($,"type","image/svg+xml"),o(p,"rel","apple-touch-icon"),o(p,"href",E=l[6].url.origin+F+"/"+me+"/apple-touch-icon.png"),o(P,"rel","manifest"),o(P,"href",z=l[6].url.origin+F+"/"+me+"/manifest.json"),o(U,"class","grid max-h-screen grid-cols-1 grid-rows-[auto,1fr,auto] max-md:hidden"),o(_,"class","text-smd grid h-full w-screen grid-cols-1 grid-rows-[auto,1fr] overflow-hidden dark:text-gray-300 md:grid-cols-[280px,1fr] md:grid-rows-[1fr]")},m(L,N){H(L,e,N),H(L,r,N),m(oe.head,n),m(oe.head,a),m(oe.head,i),m(oe.head,f),m(oe.head,c),m(oe.head,d),m(oe.head,b),m(oe.head,k),m(oe.head,$),m(oe.head,p),m(oe.head,P),H(L,C,N),H(L,_,N),Y(D,_,null),m(_,S),m(_,U),Y(O,U,null),m(_,te),K&&K.m(_,null),m(_,se),R&&R.m(_,null),m(_,le),G&&G.m(_,null),q=!0},p(L,N){var Z;(!q||N[0]&16)&&s!==(s=L[4])&&(oe.title=s),(!q||N[0]&16)&&o(f,"content",L[4]),(!q||N[0]&64&&g!==(g=""+(L[6].url.origin+F)))&&o(d,"content",g),(!q||N[0]&64&&v!==(v=L[6].url.origin+F+"/"+me+"/thumbnail.png"))&&o(b,"content",v),(!q||N[0]&64&&x!==(x=L[6].url.origin+F+"/"+me+"/favicon.ico"))&&o(k,"href",x),(!q||N[0]&64&&M!==(M=L[6].url.origin+F+"/"+me+"/icon.svg"))&&o($,"href",M),(!q||N[0]&64&&E!==(E=L[6].url.origin+F+"/"+me+"/apple-touch-icon.png"))&&o(p,"href",E),(!q||N[0]&64&&z!==(z=L[6].url.origin+F+"/"+me+"/manifest.json"))&&o(P,"href",z);const T={};N[0]&2&&(T.isOpen=L[1]),N[0]&65&&(T.title=(Z=L[0].conversations.find(L[19]))==null?void 0:Z.title),N[0]&67108901&&(T.$$scope={dirty:N,ctx:L}),D.$set(T);const h={};N[0]&1&&(h.conversations=L[0].conversations),N[0]&1&&(h.user=L[0].user),N[0]&1&&(h.canLogin=L[0].user===void 0&&L[0].loginEnabled),O.$set(h),L[3]?K?(K.p(L,N),N[0]&8&&j(K,1)):(K=et(L),K.c(),j(K,1),K.m(_,se)):K&&(we(),V(K,1,1,()=>{K=null}),ye()),L[2]?R?(R.p(L,N),N[0]&4&&j(R,1)):(R=tt(L),R.c(),j(R,1),R.m(_,le)):R&&(we(),V(R,1,1,()=>{R=null}),ye()),G&&G.p&&(!q||N[0]&67108864)&&nt(G,ge,L,L[26],q?st(ge,L[26],N,null):lt(L[26]),null)},i(L){q||(j(D.$$.fragment,L),j(O.$$.fragment,L),j(K),j(R),j(G,L),q=!0)},o(L){V(D.$$.fragment,L),V(O.$$.fragment,L),V(K),V(R),V(G,L),q=!1},d(L){L&&(u(e),u(r),u(C),u(_)),u(n),u(a),u(i),u(f),u(c),u(d),u(b),u(k),u($),u(p),u(P),ee(D),ee(O),K&&K.d(),R&&R.d(),G&&G.d(L)}}}function pr(l,e,t){let r,s,n;Ce(l,Ge,h=>t(12,r=h)),Ce(l,ke,h=>t(13,s=h)),Ce(l,ut,h=>t(6,n=h));let{$$slots:a={},$$scope:i}=e,{data:f}=e;const c="/images/AccureIQLight.png",d="/images/AccureIQDark.png";let g=!1,b=!1,v,k,x;const $=300*60*1e3;let M,p,E,P=wt;function z(){var h;(h=document.getElementById("logoutForm"))==null||h.submit(),alert("session expired, please login again")}function C(){clearTimeout(x),x=setTimeout(z,$)}let _="",D=it,S=!1;at(()=>{x=setTimeout(z,$),window.addEventListener("mousemove",C),window.addEventListener("click",C),window.addEventListener("keypress",C),_=window.location.hostname,_.includes("securegpt.accure.ai")?t(4,D="SecureGPT"):_.includes("legalgpt.accure.ai")&&t(4,D="LegalGPT");const h=()=>{var De;const Z=window.matchMedia("(prefers-color-scheme: dark)").matches,Q=document.documentElement.classList.contains("dark"),Te=document.body.classList.contains("dark"),gt=((De=getComputedStyle(document.documentElement).getPropertyValue("--is-dark-theme"))==null?void 0:De.trim())==="true";t(5,S=Z||Q||Te||gt)};h(),M=window.matchMedia("(prefers-color-scheme: dark)"),E=()=>h(),M.addEventListener("change",E),p=new MutationObserver(Z=>{for(const Q of Z)Q.type==="attributes"&&Q.attributeName==="class"&&h()}),p.observe(document.documentElement,{attributes:!0}),p.observe(document.body,{attributes:!0})});async function U(){s&&k&&s!==k&&(clearTimeout(v),t(3,k=null),await new Promise(h=>setTimeout(h,300))),t(3,k=s),v=setTimeout(()=>{be(ke,s=null,s),t(3,k=null)},3e3)}async function O(h){try{if(!(await fetch(`${F}/conversation/${h}`,{method:"DELETE",headers:{"Content-Type":"application/json"}})).ok){be(ke,s="Error while deleting conversation, try again.",s);return}n.params.id!==h?await Ve(ze.ConversationList):await yt(`${F}/`,{invalidateAll:!0})}catch(Z){console.error(Z),be(ke,s=String(Z),s)}}async function te(h,Z){try{if(!(await fetch(`${F}/conversation/${h}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:Z})})).ok){be(ke,s="Error while editing title, try again.",s);return}await Ve(ze.ConversationList)}catch(Q){console.error(Q),be(ke,s=String(Q),s)}}_t(()=>{v&&clearTimeout(v),x&&clearTimeout(x),window.removeEventListener("mousemove",C),window.removeEventListener("click",C),window.removeEventListener("keypress",C),M&&E&&M.removeEventListener("change",E),p&&p.disconnect()});const se=h=>Ue(h.detail.id,h.detail.title),le=h=>O(h.detail),q=()=>t(2,b=!0),K=h=>te(h.detail.id,h.detail.title),R=h=>h.id===n.params.id,ge=h=>t(1,g=h.detail),G=h=>Ue(h.detail.id,h.detail.title),W=h=>O(h.detail),L=()=>t(2,b=!0),N=h=>te(h.detail.id,h.detail.title),T=()=>t(2,b=!1);return l.$$set=h=>{"data"in h&&t(0,f=h.data),"$$scope"in h&&t(26,i=h.$$scope)},l.$$.update=()=>{if(l.$$.dirty[0]&8192&&s&&U(),l.$$.dirty[0]&4097&&r){const h=f.conversations.findIndex(({id:Z})=>Z===(r==null?void 0:r.convId));h!=-1&&t(0,f.conversations[h].title=(r==null?void 0:r.title)??f.conversations[h].title,f),t(0,f.conversations=[...f.conversations],f),be(Ge,r=null,r)}},[f,g,b,k,D,S,n,c,d,P,O,te,r,s,a,se,le,q,K,R,ge,G,W,L,N,T,i]}class Ar extends de{constructor(e){super(),he(this,e,pr,vr,fe,{data:0},null,[-1,-1])}}export{Ar as component};
