var V={exports:{}},g={},h={};h.__esModule=!0;h.extend=ie;h.indexOf=me;h.escapeExpression=Pe;h.isEmpty=we;h.createFrame=xe;h.blockParams=Me;h.appendContextPath=Ee;var he={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},ve=/[&<>"'`=]/g,_e=/[&<>"'`=]/;function ge(e){return he[e]}function ie(e){for(var r=1;r<arguments.length;r++)for(var t in arguments[r])Object.prototype.hasOwnProperty.call(arguments[r],t)&&(e[t]=arguments[r][t]);return e}var j=Object.prototype.toString;h.toString=j;var $=function(r){return typeof r=="function"};$(/x/)&&(h.isFunction=$=function(e){return typeof e=="function"&&j.call(e)==="[object Function]"});h.isFunction=$;var oe=Array.isArray||function(e){return e&&typeof e=="object"?j.call(e)==="[object Array]":!1};h.isArray=oe;function me(e,r){for(var t=0,i=e.length;t<i;t++)if(e[t]===r)return t;return-1}function Pe(e){if(typeof e!="string"){if(e&&e.toHTML)return e.toHTML();if(e==null)return"";if(!e)return e+"";e=""+e}return _e.test(e)?e.replace(ve,ge):e}function we(e){return!e&&e!==0?!0:!!(oe(e)&&e.length===0)}function xe(e){var r=ie({},e);return r._parent=e,r}function Me(e,r){return e.path=r,e}function Ee(e,r){return(e?e+".":"")+r}var q={exports:{}};(function(e,r){r.__esModule=!0;var t=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function i(o,l){var u=l&&l.loc,a=void 0,n=void 0,s=void 0,c=void 0;u&&(a=u.start.line,n=u.end.line,s=u.start.column,c=u.end.column,o+=" - "+a+":"+s);for(var f=Error.prototype.constructor.call(this,o),d=0;d<t.length;d++)this[t[d]]=f[t[d]];Error.captureStackTrace&&Error.captureStackTrace(this,i);try{u&&(this.lineNumber=a,this.endLineNumber=n,Object.defineProperty?(Object.defineProperty(this,"column",{value:s,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:c,enumerable:!0})):(this.column=s,this.endColumn=c))}catch{}}i.prototype=new Error,r.default=i,e.exports=r.default})(q,q.exports);var y=q.exports,C={},F={exports:{}};(function(e,r){r.__esModule=!0;var t=h;r.default=function(i){i.registerHelper("blockHelperMissing",function(o,l){var u=l.inverse,a=l.fn;if(o===!0)return a(this);if(o===!1||o==null)return u(this);if(t.isArray(o))return o.length>0?(l.ids&&(l.ids=[l.name]),i.helpers.each(o,l)):u(this);if(l.data&&l.ids){var n=t.createFrame(l.data);n.contextPath=t.appendContextPath(l.data.contextPath,l.name),l={data:n}}return a(o,l)})},e.exports=r.default})(F,F.exports);var ke=F.exports,B={exports:{}};(function(e,r){r.__esModule=!0;function t(u){return u&&u.__esModule?u:{default:u}}var i=h,o=y,l=t(o);r.default=function(u){u.registerHelper("each",function(a,n){if(!n)throw new l.default("Must pass iterator to #each");var s=n.fn,c=n.inverse,f=0,d="",v=void 0,H=void 0;n.data&&n.ids&&(H=i.appendContextPath(n.data.contextPath,n.ids[0])+"."),i.isFunction(a)&&(a=a.call(this)),n.data&&(v=i.createFrame(n.data));function I(_,R,pe){v&&(v.key=_,v.index=R,v.first=R===0,v.last=!!pe,H&&(v.contextPath=H+_)),d=d+s(a[_],{data:v,blockParams:i.blockParams([a[_],_],[H+_,null])})}if(a&&typeof a=="object")if(i.isArray(a))for(var b=a.length;f<b;f++)f in a&&I(f,f,f===a.length-1);else if(typeof Symbol=="function"&&a[Symbol.iterator]){for(var M=[],p=a[Symbol.iterator](),m=p.next();!m.done;m=p.next())M.push(m.value);a=M;for(var b=a.length;f<b;f++)I(f,f,f===a.length-1)}else(function(){var _=void 0;Object.keys(a).forEach(function(R){_!==void 0&&I(_,f-1),_=R,f++}),_!==void 0&&I(_,f-1,!0)})();return f===0&&(d=c(this)),d})},e.exports=r.default})(B,B.exports);var ye=B.exports,W={exports:{}};(function(e,r){r.__esModule=!0;function t(l){return l&&l.__esModule?l:{default:l}}var i=y,o=t(i);r.default=function(l){l.registerHelper("helperMissing",function(){if(arguments.length!==1)throw new o.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})},e.exports=r.default})(W,W.exports);var Oe=W.exports,G={exports:{}};(function(e,r){r.__esModule=!0;function t(u){return u&&u.__esModule?u:{default:u}}var i=h,o=y,l=t(o);r.default=function(u){u.registerHelper("if",function(a,n){if(arguments.length!=2)throw new l.default("#if requires exactly one argument");return i.isFunction(a)&&(a=a.call(this)),!n.hash.includeZero&&!a||i.isEmpty(a)?n.inverse(this):n.fn(this)}),u.registerHelper("unless",function(a,n){if(arguments.length!=2)throw new l.default("#unless requires exactly one argument");return u.helpers.if.call(this,a,{fn:n.inverse,inverse:n.fn,hash:n.hash})})},e.exports=r.default})(G,G.exports);var He=G.exports,U={exports:{}};(function(e,r){r.__esModule=!0,r.default=function(t){t.registerHelper("log",function(){for(var i=[void 0],o=arguments[arguments.length-1],l=0;l<arguments.length-1;l++)i.push(arguments[l]);var u=1;o.hash.level!=null?u=o.hash.level:o.data&&o.data.level!=null&&(u=o.data.level),i[0]=u,t.log.apply(t,i)})},e.exports=r.default})(U,U.exports);var Ie=U.exports,K={exports:{}};(function(e,r){r.__esModule=!0,r.default=function(t){t.registerHelper("lookup",function(i,o,l){return i&&l.lookupProperty(i,o)})},e.exports=r.default})(K,K.exports);var be=K.exports,Y={exports:{}};(function(e,r){r.__esModule=!0;function t(u){return u&&u.__esModule?u:{default:u}}var i=h,o=y,l=t(o);r.default=function(u){u.registerHelper("with",function(a,n){if(arguments.length!=2)throw new l.default("#with requires exactly one argument");i.isFunction(a)&&(a=a.call(this));var s=n.fn;if(i.isEmpty(a))return n.inverse(this);var c=n.data;return n.data&&n.ids&&(c=i.createFrame(n.data),c.contextPath=i.appendContextPath(n.data.contextPath,n.ids[0])),s(a,{data:c,blockParams:i.blockParams([a],[c&&c.contextPath])})})},e.exports=r.default})(Y,Y.exports);var Le=Y.exports;C.__esModule=!0;C.registerDefaultHelpers=Ue;C.moveHelperToHooks=Ke;function O(e){return e&&e.__esModule?e:{default:e}}var Ce=ke,Re=O(Ce),Ae=ye,De=O(Ae),Te=Oe,Ne=O(Te),Se=He,Ve=O(Se),$e=Ie,qe=O($e),Fe=be,Be=O(Fe),We=Le,Ge=O(We);function Ue(e){Re.default(e),De.default(e),Ne.default(e),Ve.default(e),qe.default(e),Be.default(e),Ge.default(e)}function Ke(e,r,t){e.helpers[r]&&(e.hooks[r]=e.helpers[r],t||delete e.helpers[r])}var ee={},Z={exports:{}};(function(e,r){r.__esModule=!0;var t=h;r.default=function(i){i.registerDecorator("inline",function(o,l,u,a){var n=o;return l.partials||(l.partials={},n=function(s,c){var f=u.partials;u.partials=t.extend({},f,l.partials);var d=o(s,c);return u.partials=f,d}),l.partials[a.args[0]]=a.fn,n})},e.exports=r.default})(Z,Z.exports);var Ye=Z.exports;ee.__esModule=!0;ee.registerDefaultDecorators=Qe;function Ze(e){return e&&e.__esModule?e:{default:e}}var ze=Ye,Je=Ze(ze);function Qe(e){Je.default(e)}var z={exports:{}};(function(e,r){r.__esModule=!0;var t=h,i={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(l){if(typeof l=="string"){var u=t.indexOf(i.methodMap,l.toLowerCase());u>=0?l=u:l=parseInt(l,10)}return l},log:function(l){if(l=i.lookupLevel(l),typeof console<"u"&&i.lookupLevel(i.level)<=l){var u=i.methodMap[l];console[u]||(u="log");for(var a=arguments.length,n=Array(a>1?a-1:0),s=1;s<a;s++)n[s-1]=arguments[s];console[u].apply(console,n)}}};r.default=i,e.exports=r.default})(z,z.exports);var se=z.exports,L={},re={};re.__esModule=!0;re.createNewLookupObject=je;var Xe=h;function je(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return Xe.extend.apply(void 0,[Object.create(null)].concat(r))}L.__esModule=!0;L.createProtoAccessControl=ar;L.resultIsAllowed=nr;L.resetLoggedProperties=lr;function er(e){return e&&e.__esModule?e:{default:e}}var ae=re,rr=se,tr=er(rr),D=Object.create(null);function ar(e){var r=Object.create(null);r.constructor=!1,r.__defineGetter__=!1,r.__defineSetter__=!1,r.__lookupGetter__=!1;var t=Object.create(null);return t.__proto__=!1,{properties:{whitelist:ae.createNewLookupObject(t,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:ae.createNewLookupObject(r,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}}function nr(e,r,t){return ne(typeof e=="function"?r.methods:r.properties,t)}function ne(e,r){return e.whitelist[r]!==void 0?e.whitelist[r]===!0:e.defaultValue!==void 0?e.defaultValue:(ur(r),!1)}function ur(e){D[e]!==!0&&(D[e]=!0,tr.default.log("error",'Handlebars: Access has been denied to resolve the property "'+e+`" because it is not an "own property" of its parent.
You can add a runtime option to disable the check or this warning:
See https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details`))}function lr(){Object.keys(D).forEach(function(e){delete D[e]})}g.__esModule=!0;g.HandlebarsEnvironment=J;function fe(e){return e&&e.__esModule?e:{default:e}}var k=h,ir=y,N=fe(ir),or=C,sr=ee,fr=se,T=fe(fr),cr=L,dr="4.7.8";g.VERSION=dr;var pr=8;g.COMPILER_REVISION=pr;var hr=7;g.LAST_COMPATIBLE_COMPILER_REVISION=hr;var vr={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};g.REVISION_CHANGES=vr;var S="[object Object]";function J(e,r,t){this.helpers=e||{},this.partials=r||{},this.decorators=t||{},or.registerDefaultHelpers(this),sr.registerDefaultDecorators(this)}J.prototype={constructor:J,logger:T.default,log:T.default.log,registerHelper:function(r,t){if(k.toString.call(r)===S){if(t)throw new N.default("Arg not supported with multiple helpers");k.extend(this.helpers,r)}else this.helpers[r]=t},unregisterHelper:function(r){delete this.helpers[r]},registerPartial:function(r,t){if(k.toString.call(r)===S)k.extend(this.partials,r);else{if(typeof t>"u")throw new N.default('Attempting to register a partial called "'+r+'" as undefined');this.partials[r]=t}},unregisterPartial:function(r){delete this.partials[r]},registerDecorator:function(r,t){if(k.toString.call(r)===S){if(t)throw new N.default("Arg not supported with multiple decorators");k.extend(this.decorators,r)}else this.decorators[r]=t},unregisterDecorator:function(r){delete this.decorators[r]},resetLoggedPropertyAccesses:function(){cr.resetLoggedProperties()}};var _r=T.default.log;g.log=_r;g.createFrame=k.createFrame;g.logger=T.default;var Q={exports:{}};(function(e,r){r.__esModule=!0;function t(i){this.string=i}t.prototype.toString=t.prototype.toHTML=function(){return""+this.string},r.default=t,e.exports=r.default})(Q,Q.exports);var gr=Q.exports,E={},te={};te.__esModule=!0;te.wrapHelper=mr;function mr(e,r){if(typeof e!="function")return e;var t=function(){var o=arguments[arguments.length-1];return arguments[arguments.length-1]=r(o),e.apply(this,arguments)};return t}E.__esModule=!0;E.checkRevision=kr;E.template=yr;E.wrapProgram=A;E.resolvePartial=Or;E.invokePartial=Hr;E.noop=ce;function Pr(e){return e&&e.__esModule?e:{default:e}}function wr(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t]);return r.default=e,r}var xr=h,P=wr(xr),Mr=y,w=Pr(Mr),x=g,ue=C,Er=te,le=L;function kr(e){var r=e&&e[0]||1,t=x.COMPILER_REVISION;if(!(r>=x.LAST_COMPATIBLE_COMPILER_REVISION&&r<=x.COMPILER_REVISION))if(r<x.LAST_COMPATIBLE_COMPILER_REVISION){var i=x.REVISION_CHANGES[t],o=x.REVISION_CHANGES[r];throw new w.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+i+") or downgrade your runtime to an older version ("+o+").")}else throw new w.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}function yr(e,r){if(!r)throw new w.default("No environment passed to template");if(!e||!e.main)throw new w.default("Unknown template object: "+typeof e);e.main.decorator=e.main_d,r.VM.checkRevision(e.compiler);var t=e.compiler&&e.compiler[0]===7;function i(u,a,n){n.hash&&(a=P.extend({},a,n.hash),n.ids&&(n.ids[0]=!0)),u=r.VM.resolvePartial.call(this,u,a,n);var s=P.extend({},n,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),c=r.VM.invokePartial.call(this,u,a,s);if(c==null&&r.compile&&(n.partials[n.name]=r.compile(u,e.compilerOptions,r),c=n.partials[n.name](a,s)),c!=null){if(n.indent){for(var f=c.split(`
`),d=0,v=f.length;d<v&&!(!f[d]&&d+1===v);d++)f[d]=n.indent+f[d];c=f.join(`
`)}return c}else throw new w.default("The partial "+n.name+" could not be compiled when running in runtime-only mode")}var o={strict:function(a,n,s){if(!a||!(n in a))throw new w.default('"'+n+'" not defined in '+a,{loc:s});return o.lookupProperty(a,n)},lookupProperty:function(a,n){var s=a[n];if(s==null||Object.prototype.hasOwnProperty.call(a,n)||le.resultIsAllowed(s,o.protoAccessControl,n))return s},lookup:function(a,n){for(var s=a.length,c=0;c<s;c++){var f=a[c]&&o.lookupProperty(a[c],n);if(f!=null)return a[c][n]}},lambda:function(a,n){return typeof a=="function"?a.call(n):a},escapeExpression:P.escapeExpression,invokePartial:i,fn:function(a){var n=e[a];return n.decorator=e[a+"_d"],n},programs:[],program:function(a,n,s,c,f){var d=this.programs[a],v=this.fn(a);return n||f||c||s?d=A(this,a,v,n,s,c,f):d||(d=this.programs[a]=A(this,a,v)),d},data:function(a,n){for(;a&&n--;)a=a._parent;return a},mergeIfNeeded:function(a,n){var s=a||n;return a&&n&&a!==n&&(s=P.extend({},n,a)),s},nullContext:Object.seal({}),noop:r.VM.noop,compilerInfo:e.compiler};function l(u){var a=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],n=a.data;l._setup(a),!a.partial&&e.useData&&(n=Ir(u,n));var s=void 0,c=e.useBlockParams?[]:void 0;e.useDepths&&(a.depths?s=u!=a.depths[0]?[u].concat(a.depths):a.depths:s=[u]);function f(d){return""+e.main(o,d,o.helpers,o.partials,n,c,s)}return f=de(e.main,f,o,a.depths||[],n,c),f(u,a)}return l.isTop=!0,l._setup=function(u){if(u.partial)o.protoAccessControl=u.protoAccessControl,o.helpers=u.helpers,o.partials=u.partials,o.decorators=u.decorators,o.hooks=u.hooks;else{var a=P.extend({},r.helpers,u.helpers);br(a,o),o.helpers=a,e.usePartial&&(o.partials=o.mergeIfNeeded(u.partials,r.partials)),(e.usePartial||e.useDecorators)&&(o.decorators=P.extend({},r.decorators,u.decorators)),o.hooks={},o.protoAccessControl=le.createProtoAccessControl(u);var n=u.allowCallsToHelperMissing||t;ue.moveHelperToHooks(o,"helperMissing",n),ue.moveHelperToHooks(o,"blockHelperMissing",n)}},l._child=function(u,a,n,s){if(e.useBlockParams&&!n)throw new w.default("must pass block params");if(e.useDepths&&!s)throw new w.default("must pass parent depths");return A(o,u,e[u],a,0,n,s)},l}function A(e,r,t,i,o,l,u){function a(n){var s=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],c=u;return u&&n!=u[0]&&!(n===e.nullContext&&u[0]===null)&&(c=[n].concat(u)),t(e,n,e.helpers,e.partials,s.data||i,l&&[s.blockParams].concat(l),c)}return a=de(t,a,e,u,i,l),a.program=r,a.depth=u?u.length:0,a.blockParams=o||0,a}function Or(e,r,t){return e?!e.call&&!t.name&&(t.name=e,e=t.partials[e]):t.name==="@partial-block"?e=t.data["partial-block"]:e=t.partials[t.name],e}function Hr(e,r,t){var i=t.data&&t.data["partial-block"];t.partial=!0,t.ids&&(t.data.contextPath=t.ids[0]||t.data.contextPath);var o=void 0;if(t.fn&&t.fn!==ce&&function(){t.data=x.createFrame(t.data);var l=t.fn;o=t.data["partial-block"]=function(a){var n=arguments.length<=1||arguments[1]===void 0?{}:arguments[1];return n.data=x.createFrame(n.data),n.data["partial-block"]=i,l(a,n)},l.partials&&(t.partials=P.extend({},t.partials,l.partials))}(),e===void 0&&o&&(e=o),e===void 0)throw new w.default("The partial "+t.name+" could not be found");if(e instanceof Function)return e(r,t)}function ce(){return""}function Ir(e,r){return(!r||!("root"in r))&&(r=r?x.createFrame(r):{},r.root=e),r}function de(e,r,t,i,o,l){if(e.decorator){var u={};r=e.decorator(r,u,t,i&&i[0],o,l,i),P.extend(r,u)}return r}function br(e,r){Object.keys(e).forEach(function(t){var i=e[t];e[t]=Lr(i,r)})}function Lr(e,r){var t=r.lookupProperty;return Er.wrapHelper(e,function(i){return P.extend({lookupProperty:t},i)})}var X={exports:{}};(function(e,r){r.__esModule=!0,r.default=function(t){(function(){typeof globalThis!="object"&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__)})();var i=globalThis.Handlebars;t.noConflict=function(){return globalThis.Handlebars===t&&(globalThis.Handlebars=i),t}},e.exports=r.default})(X,X.exports);var Cr=X.exports;(function(e,r){r.__esModule=!0;function t(p){return p&&p.__esModule?p:{default:p}}function i(p){if(p&&p.__esModule)return p;var m={};if(p!=null)for(var _ in p)Object.prototype.hasOwnProperty.call(p,_)&&(m[_]=p[_]);return m.default=p,m}var o=g,l=i(o),u=gr,a=t(u),n=y,s=t(n),c=h,f=i(c),d=E,v=i(d),H=Cr,I=t(H);function b(){var p=new l.HandlebarsEnvironment;return f.extend(p,l),p.SafeString=a.default,p.Exception=s.default,p.Utils=f,p.escapeExpression=f.escapeExpression,p.VM=v,p.template=function(m){return v.template(m,p)},p}var M=b();M.create=b,I.default(M),M.default=M,r.default=M,e.exports=r.default})(V,V.exports);var Rr=V.exports;export{g as b,y as e,Rr as h,Cr as n,h as u};
