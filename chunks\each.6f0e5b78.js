import{t as q,b as z}from"./index.03cf2e9a.js";import{v as B}from"./scheduler.91cfa29b.js";function E(n){return(n==null?void 0:n.length)!==void 0?n:Array.from(n)}function F(n,f){n.d(1),f.delete(n.key)}function G(n,f){z(n,1,1,()=>{f.delete(n.key)})}function H(n,f,k,x,A,g,h,_,p,b,w,j){let i=n.length,d=g.length,c=i;const u={};for(;c--;)u[n[c].key]=c;const l=[],a=new Map,m=new Map,v=[];for(c=d;c--;){const e=j(A,g,c),s=k(e);let t=h.get(s);t?x&&v.push(()=>t.p(e,f)):(t=b(s,e),t.c()),a.set(s,l[c]=t),s in u&&m.set(s,Math.abs(c-u[s]))}const M=new Set,S=new Set;function y(e){q(e,1),e.m(_,w),h.set(e.key,e),w=e.first,d--}for(;i&&d;){const e=l[d-1],s=n[i-1],t=e.key,o=s.key;e===s?(w=e.first,i--,d--):a.has(o)?!h.has(t)||M.has(t)?y(e):S.has(o)?i--:m.get(t)>m.get(o)?(S.add(t),y(e)):(M.add(o),i--):(p(s,h),i--)}for(;i--;){const e=n[i];a.has(e.key)||p(e,h)}for(;d;)y(l[d-1]);return B(v),l}export{F as d,E as e,G as o,H as u};
