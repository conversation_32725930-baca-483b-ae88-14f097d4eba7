const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3000;

// Serve static files from the current directory
app.use(express.static('.', {
  setHeaders: (res, path) => {
    // Set proper MIME types for SvelteKit files
    if (path.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript');
    } else if (path.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css');
    } else if (path.endsWith('.json')) {
      res.setHeader('Content-Type', 'application/json');
    }
  }
}));

// Handle SvelteKit routing - serve index.html for all routes
app.get('*', (req, res) => {
  // Check if it's a static file request
  const filePath = path.join(__dirname, req.path);
  if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
    return res.sendFile(filePath);
  }

  // For SvelteKit apps, we need to create a basic HTML file that loads the app
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="/kg.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Accure SecureGPT</title>
    
    <!-- Load CSS files -->
    <link rel="stylesheet" href="/assets/main.c519ac7b.css">
    <link rel="stylesheet" href="/assets/forms.640fb8f2.css">
    <link rel="stylesheet" href="/assets/AdminNavBar.c5b178b0.css">
    <link rel="stylesheet" href="/assets/FileUploader.d9ac2529.css">
    <link rel="stylesheet" href="/assets/pendingMessage.c6461223.css">
    <link rel="stylesheet" href="/assets/4.62dafba3.css">
    <link rel="stylesheet" href="/assets/5.00584e21.css">
    <link rel="stylesheet" href="/assets/7.f1d4aa1c.css">
    <link rel="stylesheet" href="/assets/12.02fd05f2.css">
    
    <!-- Preload key modules -->
    <link rel="modulepreload" href="/entry/start.5fd2019a.js">
    <link rel="modulepreload" href="/entry/app.b53eae29.js">
</head>
<body data-sveltekit-preload-data="hover">
    <div style="display: contents">
        <div id="svelte">
            <!-- SvelteKit app will mount here -->
            <div style="padding: 20px; text-align: center;">
                <h1>Loading Accure SecureGPT...</h1>
                <p>Please wait while the application loads.</p>
            </div>
        </div>
    </div>

    <!-- Load the SvelteKit application -->
    <script type="module">
        // Set up the environment
        window.__sveltekit = {
            base: '',
            assets: '',
            env: {
                PUBLIC_API_URL: 'http://localhost:9001'
            }
        };
        
        // Load the start module
        import('/entry/start.5fd2019a.js').then(module => {
            console.log('SvelteKit app loaded');
        }).catch(error => {
            console.error('Failed to load SvelteKit app:', error);
            document.getElementById('svelte').innerHTML = \`
                <div style="padding: 20px; text-align: center; color: red;">
                    <h1>Failed to Load Application</h1>
                    <p>Error: \${error.message}</p>
                    <p>Please check the browser console for more details.</p>
                    <p>Make sure the backend server is running on port 9001.</p>
                </div>
            \`;
        });
    </script>
</body>
</html>
  `;
  
  res.setHeader('Content-Type', 'text/html');
  res.send(html);
});

app.listen(PORT, () => {
  console.log(`🌐 Frontend server running on http://localhost:${PORT}`);
  console.log(`📁 Serving files from: ${__dirname}`);
  console.log(`🔗 Backend API: http://localhost:9001`);
  console.log('');
  console.log('🚀 Open http://localhost:3000 in your browser to test the application');
});
