import{s as i,e as n,c,p as o,g as v,j as d,u as t,d as m}from"./scheduler.91cfa29b.js";import{S as u,i as f}from"./index.03cf2e9a.js";import{s as y}from"./sessionStore.520658ac.js";function h(a){let e,l='<div class="navbar svelte-yl073c"><ul class="svelte-yl073c"><li class="svelte-yl073c"><a href="/admin/users" class="svelte-yl073c">Users</a></li> <li class="svelte-yl073c"><a href="/admin/users/add" class="svelte-yl073c">Add User</a></li> <li class="svelte-yl073c"><a href="/admin/groups" class="svelte-yl073c">Groups</a></li></ul></div>';return{c(){e=n("div"),e.innerHTML=l,this.h()},l(s){e=c(s,"DIV",{class:!0,"data-svelte-h":!0}),o(e)!=="svelte-n0y9e1"&&(e.innerHTML=l),this.h()},h(){v(e,"class","nav-container svelte-yl073c")},m(s,r){d(s,e,r)},p:t,i:t,o:t,d(s){s&&m(e)}}}function p(a){return y.subscribe(e=>{e.email,e.org,e.role}),[]}class A extends u{constructor(e){super(),f(this,e,p,h,i,{})}}export{A};
