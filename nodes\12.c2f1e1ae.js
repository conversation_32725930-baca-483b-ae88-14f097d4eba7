import{s as kt,e as g,a as S,F as we,c as v,n as k,p as G,f as N,d as m,g as h,j as H,i as u,y as ye,r as W,u as ge,v as ke,o as Ie,z as Ce,t as Q,q as Y,x as ae,N as Et,O as Ct,b as Tt,R as Bt,H as Dt,E as Lt,w as Se,l as Ue}from"../chunks/scheduler.91cfa29b.js";import{S as Pt,i as xt,b as Ne,f as Ot,t as Me,g as Ut,c as At,a as It,m as St,d as Nt}from"../chunks/index.03cf2e9a.js";import{g as Mt,P as Vt,a as Ht}from"../chunks/public.44f51b11.js";import{e as ve}from"../chunks/each.6f0e5b78.js";import{s as Ft}from"../chunks/sessionStore.520658ac.js";import{F as zt}from"../chunks/FileUploader.c11bce16.js";import{p as qt}from"../chunks/stores.33557e05.js";import{a as Te}from"../chunks/axios.1f2f0c0e.js";import{w as jt}from"../chunks/paths.e40a44b0.js";import{component as Ve}from"./1.fbd3b195.js";/* empty css                     */import{m as Fe}from"../chunks/marked.esm.76161808.js";const{window:Wt}=Mt;function ze(l,e,t){const r=l.slice();return r[50]=e[t],r}function qe(l,e,t){const r=l.slice();return r[53]=e[t],r}function je(l,e,t){const r=l.slice();return r[56]=e[t],r}function We(l){let e,t="This field is required.";return{c(){e=g("p"),e.textContent=t,this.h()},l(r){e=v(r,"P",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-f8he5b"&&(e.textContent=t),this.h()},h(){h(e,"class","mt-1 text-sm text-red-600")},m(r,d){H(r,e,d)},d(r){r&&m(e)}}}function Re(l){let e,t="This field is required.";return{c(){e=g("p"),e.textContent=t,this.h()},l(r){e=v(r,"P",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-f8he5b"&&(e.textContent=t),this.h()},h(){h(e,"class","mt-1 text-sm text-red-600")},m(r,d){H(r,e,d)},d(r){r&&m(e)}}}function Qe(l){let e,t,r="Write SQL Query",d,o,n,c,a,s,f="or",i,p,b="Select Tables",_,D,C,y,E='<tr><th class="px-4 py-3 svelte-1ulb020">Select</th> <th class="px-4 py-3 svelte-1ulb020">Table Name</th></tr>',M,L,P,w,T=ve(l[18]),I=[];for(let x=0;x<T.length;x+=1)I[x]=Ye(je(l,T,x));return{c(){e=g("div"),t=g("p"),t.textContent=r,d=S(),o=g("textarea"),n=S(),c=g("br"),a=S(),s=g("h2"),s.textContent=f,i=S(),p=g("p"),p.textContent=b,_=S(),D=g("div"),C=g("table"),y=g("thead"),y.innerHTML=E,M=S(),L=g("tbody");for(let x=0;x<I.length;x+=1)I[x].c();this.h()},l(x){e=v(x,"DIV",{class:!0});var V=k(e);t=v(V,"P",{class:!0,"data-svelte-h":!0}),G(t)!=="svelte-rzlsje"&&(t.textContent=r),d=N(V),o=v(V,"TEXTAREA",{placeholder:!0,rows:!0,class:!0}),k(o).forEach(m),n=N(V),c=v(V,"BR",{}),a=N(V),s=v(V,"H2",{class:!0,"data-svelte-h":!0}),G(s)!=="svelte-1plnbjr"&&(s.textContent=f),i=N(V),p=v(V,"P",{class:!0,"data-svelte-h":!0}),G(p)!=="svelte-g67p44"&&(p.textContent=b),_=N(V),D=v(V,"DIV",{class:!0});var B=k(D);C=v(B,"TABLE",{class:!0});var z=k(C);y=v(z,"THEAD",{class:!0,"data-svelte-h":!0}),G(y)!=="svelte-o1zekc"&&(y.innerHTML=E),M=N(z),L=v(z,"TBODY",{class:!0});var X=k(L);for(let ie=0;ie<I.length;ie+=1)I[ie].l(X);X.forEach(m),z.forEach(m),B.forEach(m),V.forEach(m),this.h()},h(){h(t,"class","mb-2 text-lg font-semibold"),h(o,"placeholder","SELECT * FROM users WHERE id = ?;"),h(o,"rows","3"),h(o,"class","w-full resize-y rounded-lg border border-gray-300 p-3 shadow-sm transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"),h(s,"class","svelte-1ulb020"),h(p,"class","mb-2 text-lg font-semibold"),h(y,"class","bg-gray-100 uppercase tracking-wider text-gray-700 dark:bg-gray-800 dark:text-gray-200"),h(L,"class","divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900"),h(C,"class","min-w-full divide-y divide-gray-200 text-left text-sm dark:divide-gray-700 svelte-1ulb020"),h(D,"class","overflow-x-auto rounded-xl border border-gray-200 shadow-md dark:border-gray-700"),h(e,"class","selector-container svelte-1ulb020")},m(x,V){H(x,e,V),u(e,t),u(e,d),u(e,o),ye(o,l[6]),u(e,n),u(e,c),u(e,a),u(e,s),u(e,i),u(e,p),u(e,_),u(e,D),u(D,C),u(C,y),u(C,M),u(C,L);for(let B=0;B<I.length;B+=1)I[B]&&I[B].m(L,null);P||(w=W(o,"input",l[40]),P=!0)},p(x,V){if(V[0]&64&&ye(o,x[6]),V[0]&134610944){T=ve(x[18]);let B;for(B=0;B<T.length;B+=1){const z=je(x,T,B);I[B]?I[B].p(z,V):(I[B]=Ye(z),I[B].c(),I[B].m(L,null))}for(;B<I.length;B+=1)I[B].d(1);I.length=T.length}},d(x){x&&m(e),Ce(I,x),P=!1,w()}}}function Ye(l){let e,t,r,d,o,n,c=l[56]+"",a,s,f,i;function p(...b){return l[41](l[56],...b)}return{c(){e=g("tr"),t=g("td"),r=g("input"),o=S(),n=g("td"),a=Q(c),s=S(),this.h()},l(b){e=v(b,"TR",{class:!0});var _=k(e);t=v(_,"TD",{class:!0});var D=k(t);r=v(D,"INPUT",{type:!0,class:!0}),D.forEach(m),o=N(_),n=v(_,"TD",{class:!0});var C=k(n);a=Y(C,c),C.forEach(m),s=N(_),_.forEach(m),this.h()},h(){h(r,"type","checkbox"),r.checked=d=l[17].includes(l[56]),h(r,"class","accent-blue-600 svelte-1ulb020"),h(t,"class","px-4 py-3 svelte-1ulb020"),h(n,"class","px-4 py-3 text-gray-900 dark:text-gray-100 svelte-1ulb020"),h(e,"class","transition hover:bg-gray-50 dark:hover:bg-gray-800")},m(b,_){H(b,e,_),u(e,t),u(t,r),u(e,o),u(e,n),u(n,a),u(e,s),f||(i=W(r,"change",p),f=!0)},p(b,_){l=b,_[0]&393216&&d!==(d=l[17].includes(l[56]))&&(r.checked=d),_[0]&262144&&c!==(c=l[56]+"")&&ae(a,c)},d(b){b&&m(e),f=!1,i()}}}function Ge(l){let e,t,r,d,o,n,c,a,s,f,i,p,b,_,D,C;return{c(){e=g("div"),t=g("button"),r=Q("← Prev"),o=S(),n=g("span"),c=Q("Page "),a=Q(l[0]),s=Q(" of "),f=Q(l[21]),i=S(),p=g("button"),b=Q("Next →"),this.h()},l(y){e=v(y,"DIV",{class:!0});var E=k(e);t=v(E,"BUTTON",{class:!0});var M=k(t);r=Y(M,"← Prev"),M.forEach(m),o=N(E),n=v(E,"SPAN",{class:!0});var L=k(n);c=Y(L,"Page "),a=Y(L,l[0]),s=Y(L," of "),f=Y(L,l[21]),L.forEach(m),i=N(E),p=v(E,"BUTTON",{class:!0});var P=k(p);b=Y(P,"Next →"),P.forEach(m),E.forEach(m),this.h()},h(){t.disabled=d=l[0]===1,h(t,"class","svelte-1ulb020"),h(n,"class","svelte-1ulb020"),p.disabled=_=l[0]===l[21],h(p,"class","svelte-1ulb020"),h(e,"class","pagination svelte-1ulb020")},m(y,E){H(y,e,E),u(e,t),u(t,r),u(e,o),u(e,n),u(n,c),u(n,a),u(n,s),u(n,f),u(e,i),u(e,p),u(p,b),D||(C=[W(t,"click",l[29]),W(p,"click",l[28])],D=!0)},p(y,E){E[0]&1&&d!==(d=y[0]===1)&&(t.disabled=d),E[0]&1&&ae(a,y[0]),E[0]&2097152&&ae(f,y[21]),E[0]&2097153&&_!==(_=y[0]===y[21])&&(p.disabled=_)},d(y){y&&m(e),D=!1,ke(C)}}}function Ke(l){let e,t=l[11]?"processing...":"process",r,d,o;return{c(){e=g("button"),r=Q(t),this.h()},l(n){e=v(n,"BUTTON",{class:!0});var c=k(e);r=Y(c,t),c.forEach(m),this.h()},h(){h(e,"class","rounded-md bg-blue-600 px-4 py-2 font-semibold text-white shadow hover:bg-blue-700")},m(n,c){H(n,e,c),u(e,r),d||(o=W(e,"click",l[31]),d=!0)},p(n,c){c[0]&2048&&t!==(t=n[11]?"processing...":"process")&&ae(r,t)},d(n){n&&m(e),d=!1,o()}}}function Rt(l){let e,t,r,d,o,n,c;function a(i,p){return i[3].length===0?Gt:Yt}let s=a(l),f=s(l);return{c(){e=g("div"),t=g("div"),r=g("input"),d=S(),o=g("div"),f.c(),this.h()},l(i){e=v(i,"DIV",{class:!0});var p=k(e);t=v(p,"DIV",{class:!0});var b=k(t);r=v(b,"INPUT",{type:!0,placeholder:!0,class:!0}),b.forEach(m),p.forEach(m),d=N(i),o=v(i,"DIV",{class:!0});var _=k(o);f.l(_),_.forEach(m),this.h()},h(){h(r,"type","text"),h(r,"placeholder","🔍 Search..."),h(r,"class","w-full rounded-full border border-gray-300 bg-white px-5 py-2.5 text-sm text-gray-700 shadow-sm transition focus:border-indigo-500 focus:outline-none focus:ring focus:ring-indigo-200"),h(t,"class","relative mx-auto max-w-md"),h(e,"class","mt-2 px-4 md:px-10 lg:px-20"),h(o,"class","mt-1 w-full space-y-0 px-4 md:px-10 lg:px-20")},m(i,p){H(i,e,p),u(e,t),u(t,r),ye(r,l[2]),H(i,d,p),H(i,o,p),f.m(o,null),n||(c=W(r,"input",l[42]),n=!0)},p(i,p){p[0]&4&&r.value!==i[2]&&ye(r,i[2]),s===(s=a(i))&&f?f.p(i,p):(f.d(1),f=s(i),f&&(f.c(),f.m(o,null)))},d(i){i&&(m(e),m(d),m(o)),f.d(),n=!1,c()}}}function Qt(l){let e,t="No data available.";return{c(){e=g("p"),e.textContent=t,this.h()},l(r){e=v(r,"P",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-16nbllr"&&(e.textContent=t),this.h()},h(){h(e,"class","mt-6 text-center text-gray-500")},m(r,d){H(r,e,d)},p:ge,d(r){r&&m(e)}}}function Yt(l){let e,t,r=ve(l[23]),d=[];for(let n=0;n<r.length;n+=1)d[n]=$e(ze(l,r,n));let o=l[22]>1&&et(l);return{c(){for(let n=0;n<d.length;n+=1)d[n].c();e=S(),o&&o.c(),t=we()},l(n){for(let c=0;c<d.length;c+=1)d[c].l(n);e=N(n),o&&o.l(n),t=we()},m(n,c){for(let a=0;a<d.length;a+=1)d[a]&&d[a].m(n,c);H(n,e,c),o&&o.m(n,c),H(n,t,c)},p(n,c){if(c[0]&76021784){r=ve(n[23]);let a;for(a=0;a<r.length;a+=1){const s=ze(n,r,a);d[a]?d[a].p(s,c):(d[a]=$e(s),d[a].c(),d[a].m(e.parentNode,e))}for(;a<d.length;a+=1)d[a].d(1);d.length=r.length}n[22]>1?o?o.p(n,c):(o=et(n),o.c(),o.m(t.parentNode,t)):o&&(o.d(1),o=null)},d(n){n&&(m(e),m(t)),Ce(d,n),o&&o.d(n)}}}function Gt(l){let e,t="No matching directories found.";return{c(){e=g("p"),e.textContent=t,this.h()},l(r){e=v(r,"P",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-e3r8m4"&&(e.textContent=t),this.h()},h(){h(e,"class","mt-4 text-center text-gray-500")},m(r,d){H(r,e,d)},p:ge,d(r){r&&m(e)}}}function Ze(l){return{c:ge,l:ge,m:ge,d:ge}}function Xe(l){let e,t,r,d='<tr><th class="px-6 py-3 svelte-1ulb020">Source</th> <th class="px-6 py-3 svelte-1ulb020">Ingest Status</th> <th class="px-6 py-3 svelte-1ulb020">Summary</th> <th class="px-6 py-3 svelte-1ulb020">Message</th> <th class="px-6 py-3 svelte-1ulb020">Records</th></tr>',o,n,c=ve(l[4][l[50]]),a=[];for(let s=0;s<c.length;s+=1)a[s]=Je(qe(l,c,s));return{c(){e=g("div"),t=g("table"),r=g("thead"),r.innerHTML=d,o=S(),n=g("tbody");for(let s=0;s<a.length;s+=1)a[s].c();this.h()},l(s){e=v(s,"DIV",{class:!0});var f=k(e);t=v(f,"TABLE",{class:!0});var i=k(t);r=v(i,"THEAD",{class:!0,"data-svelte-h":!0}),G(r)!=="svelte-ywjmoe"&&(r.innerHTML=d),o=N(i),n=v(i,"TBODY",{class:!0});var p=k(n);for(let b=0;b<a.length;b+=1)a[b].l(p);p.forEach(m),i.forEach(m),f.forEach(m),this.h()},h(){h(r,"class","bg-gray-200 text-xs uppercase tracking-wider text-gray-600"),h(n,"class","divide-y divide-gray-200 bg-white"),h(t,"class","min-w-full text-left text-sm text-gray-700 svelte-1ulb020"),h(e,"class","overflow-x-auto")},m(s,f){H(s,e,f),u(e,t),u(t,r),u(t,o),u(t,n);for(let i=0;i<a.length;i+=1)a[i]&&a[i].m(n,null)},p(s,f){if(f[0]&8388624){c=ve(s[4][s[50]]);let i;for(i=0;i<c.length;i+=1){const p=qe(s,c,i);a[i]?a[i].p(p,f):(a[i]=Je(p),a[i].c(),a[i].m(n,null))}for(;i<a.length;i+=1)a[i].d(1);a.length=c.length}},d(s){s&&m(e),Ce(a,s)}}}function Je(l){let e,t,r=l[53].source+"",d,o,n,c=l[53].ingest_status+"",a,s,f,i,p=lt(l[53].summary)+"",b,_,D=l[53].message+"",C,y,E,M=l[53].no_of_records+"",L,P;return{c(){e=g("tr"),t=g("td"),d=Q(r),o=S(),n=g("td"),a=Q(c),s=S(),f=g("td"),i=new Dt(!1),b=S(),_=g("td"),C=Q(D),y=S(),E=g("td"),L=Q(M),P=S(),this.h()},l(w){e=v(w,"TR",{class:!0});var T=k(e);t=v(T,"TD",{class:!0});var I=k(t);d=Y(I,r),I.forEach(m),o=N(T),n=v(T,"TD",{class:!0});var x=k(n);a=Y(x,c),x.forEach(m),s=N(T),f=v(T,"TD",{class:!0});var V=k(f);i=Lt(V,!1),V.forEach(m),b=N(T),_=v(T,"TD",{class:!0});var B=k(_);C=Y(B,D),B.forEach(m),y=N(T),E=v(T,"TD",{class:!0});var z=k(E);L=Y(z,M),z.forEach(m),P=N(T),T.forEach(m),this.h()},h(){h(t,"class","px-6 py-4 svelte-1ulb020"),h(n,"class","px-6 py-4 svelte-1ulb020"),i.a=null,h(f,"class","px-6 py-4 svelte-1ulb020"),h(_,"class","px-6 py-4 svelte-1ulb020"),h(E,"class","px-6 py-4 svelte-1ulb020"),h(e,"class","hover:bg-gray-50")},m(w,T){H(w,e,T),u(e,t),u(t,d),u(e,o),u(e,n),u(n,a),u(e,s),u(e,f),i.m(p,f),u(e,b),u(e,_),u(_,C),u(e,y),u(e,E),u(E,L),u(e,P)},p(w,T){T[0]&8388624&&r!==(r=w[53].source+"")&&ae(d,r),T[0]&8388624&&c!==(c=w[53].ingest_status+"")&&ae(a,c),T[0]&8388624&&p!==(p=lt(w[53].summary)+"")&&i.p(p),T[0]&8388624&&D!==(D=w[53].message+"")&&ae(C,D),T[0]&8388624&&M!==(M=w[53].no_of_records+"")&&ae(L,M)},d(w){w&&m(e)}}}function $e(l){let e,t,r,d,o,n,c=l[50]+"",a,s,f,i,p,b,_=l[50]===l[3][0]&&Ze();function D(){return l[43](l[50])}let C=l[19][l[50]]&&Xe(l);return{c(){e=g("div"),t=g("div"),r=g("div"),d=g("img"),n=S(),a=Q(c),s=S(),_&&_.c(),f=S(),C&&C.c(),i=S(),this.h()},l(y){e=v(y,"DIV",{class:!0});var E=k(e);t=v(E,"DIV",{class:!0});var M=k(t);r=v(M,"DIV",{class:!0});var L=k(r);d=v(L,"IMG",{src:!0,alt:!0,width:!0,height:!0}),n=N(L),a=Y(L,c),s=N(L),_&&_.l(L),L.forEach(m),M.forEach(m),f=N(E),C&&C.l(E),i=N(E),E.forEach(m),this.h()},h(){Bt(d.src,o="/chatui/project-database-.webp")||h(d,"src",o),h(d,"alt","Icon"),h(d,"width","18"),h(d,"height","20"),h(r,"class","flex items-center gap-3 text-base font-semibold text-gray-800"),h(t,"class","flex cursor-pointer items-center justify-between bg-gray-50 px-4 py-3 transition-all duration-300 hover:bg-gray-100"),h(e,"class","border-b border-gray-200")},m(y,E){H(y,e,E),u(e,t),u(t,r),u(r,d),u(r,n),u(r,a),u(r,s),_&&_.m(r,null),u(e,f),C&&C.m(e,null),u(e,i),p||(b=W(t,"click",D),p=!0)},p(y,E){l=y,E[0]&8388608&&c!==(c=l[50]+"")&&ae(a,c),l[50]===l[3][0]?_||(_=Ze(),_.c(),_.m(r,null)):_&&(_.d(1),_=null),l[19][l[50]]?C?C.p(l,E):(C=Xe(l),C.c(),C.m(e,i)):C&&(C.d(1),C=null)},d(y){y&&m(e),_&&_.d(),C&&C.d(),p=!1,b()}}}function et(l){let e,t,r,d,o,n,c,a,s,f,i,p,b,_,D,C;return{c(){e=g("div"),t=g("button"),r=Q("Previous"),o=S(),n=g("span"),c=Q("Page "),a=Q(l[1]),s=Q(" of "),f=Q(l[22]),i=S(),p=g("button"),b=Q("Next"),this.h()},l(y){e=v(y,"DIV",{class:!0});var E=k(e);t=v(E,"BUTTON",{class:!0});var M=k(t);r=Y(M,"Previous"),M.forEach(m),o=N(E),n=v(E,"SPAN",{class:!0});var L=k(n);c=Y(L,"Page "),a=Y(L,l[1]),s=Y(L," of "),f=Y(L,l[22]),L.forEach(m),i=N(E),p=v(E,"BUTTON",{class:!0});var P=k(p);b=Y(P,"Next"),P.forEach(m),E.forEach(m),this.h()},h(){h(t,"class","rounded bg-gray-200 px-4 py-2 text-sm text-gray-700 hover:bg-gray-300 disabled:opacity-50"),t.disabled=d=l[1]===1,h(n,"class","text-sm text-gray-600"),h(p,"class","rounded bg-gray-200 px-4 py-2 text-sm text-gray-700 hover:bg-gray-300 disabled:opacity-50"),p.disabled=_=l[1]===l[22],h(e,"class","mt-4 flex justify-center gap-4")},m(y,E){H(y,e,E),u(e,t),u(t,r),u(e,o),u(e,n),u(n,c),u(n,a),u(n,s),u(n,f),u(e,i),u(e,p),u(p,b),D||(C=[W(t,"click",l[25]),W(p,"click",l[24])],D=!0)},p(y,E){E[0]&2&&d!==(d=y[1]===1)&&(t.disabled=d),E[0]&2&&ae(a,y[1]),E[0]&4194304&&ae(f,y[22]),E[0]&4194306&&_!==(_=y[1]===y[22])&&(p.disabled=_)},d(y){y&&m(e),D=!1,ke(C)}}}function tt(l){let e,t,r,d,o,n,c="×",a,s,f,i;function p(D,C){return D[15]?Xt:D[16]?Zt:Kt}let b=p(l),_=b(l);return{c(){e=g("div"),t=g("div"),r=g("div"),d=g("h2"),o=S(),n=g("button"),n.textContent=c,a=S(),s=g("div"),_.c(),this.h()},l(D){e=v(D,"DIV",{class:!0,role:!0,"aria-modal":!0});var C=k(e);t=v(C,"DIV",{class:!0});var y=k(t);r=v(y,"DIV",{class:!0});var E=k(r);d=v(E,"H2",{class:!0}),k(d).forEach(m),o=N(E),n=v(E,"BUTTON",{class:!0,"aria-label":!0,"data-svelte-h":!0}),G(n)!=="svelte-s5zcta"&&(n.textContent=c),E.forEach(m),a=N(y),s=v(y,"DIV",{class:!0});var M=k(s);_.l(M),M.forEach(m),y.forEach(m),C.forEach(m),this.h()},h(){h(d,"class","svelte-1ulb020"),h(n,"class","close-button svelte-1ulb020"),h(n,"aria-label","Close modal"),h(r,"class","modal-header svelte-1ulb020"),h(s,"class","modal-content svelte-1ulb020"),h(t,"class","modal-container svelte-1ulb020"),h(e,"class","modal-overlay svelte-1ulb020"),h(e,"role","dialog"),h(e,"aria-modal","true")},m(D,C){H(D,e,C),u(e,t),u(t,r),u(r,d),u(r,o),u(r,n),u(t,a),u(t,s),_.m(s,null),f||(i=[W(n,"click",l[33]),W(t,"click",Et(l[37])),W(e,"click",l[33])],f=!0)},p(D,C){b===(b=p(D))&&_?_.p(D,C):(_.d(1),_=b(D),_&&(_.c(),_.m(s,null)))},d(D){D&&m(e),_.d(),f=!1,ke(i)}}}function Kt(l){let e;return{c(){e=g("iframe"),this.h()},l(t){e=v(t,"IFRAME",{class:!0,title:!0,srcdoc:!0,sandbox:!0}),k(e).forEach(m),this.h()},h(){h(e,"class","content-iframe svelte-1ulb020"),h(e,"title",l[14]),h(e,"srcdoc",l[13]),h(e,"sandbox","allow-same-origin allow-scripts allow-forms")},m(t,r){H(t,e,r),l[44](e)},p(t,r){r[0]&16384&&h(e,"title",t[14]),r[0]&8192&&h(e,"srcdoc",t[13])},d(t){t&&m(e),l[44](null)}}}function Zt(l){let e,t,r="Error Loading Content",d,o,n="Sorry, we couldn't load the requested file.",c,a,s;return{c(){e=g("div"),t=g("h4"),t.textContent=r,d=S(),o=g("p"),o.textContent=n,c=S(),a=g("div"),s=Q(l[16]),this.h()},l(f){e=v(f,"DIV",{class:!0});var i=k(e);t=v(i,"H4",{"data-svelte-h":!0}),G(t)!=="svelte-1cp7uz"&&(t.textContent=r),d=N(i),o=v(i,"P",{"data-svelte-h":!0}),G(o)!=="svelte-xb86a4"&&(o.textContent=n),c=N(i),a=v(i,"DIV",{class:!0});var p=k(a);s=Y(p,l[16]),p.forEach(m),i.forEach(m),this.h()},h(){h(a,"class","error-details svelte-1ulb020"),h(e,"class","modal-error svelte-1ulb020")},m(f,i){H(f,e,i),u(e,t),u(e,d),u(e,o),u(e,c),u(e,a),u(a,s)},p(f,i){i[0]&65536&&ae(s,f[16])},d(f){f&&m(e)}}}function Xt(l){let e,t='<div class="spinner svelte-1ulb020"></div> <p>Loading content...</p>';return{c(){e=g("div"),e.innerHTML=t,this.h()},l(r){e=v(r,"DIV",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-61c640"&&(e.innerHTML=t),this.h()},h(){h(e,"class","modal-loading svelte-1ulb020")},m(r,d){H(r,e,d)},p:ge,d(r){r&&m(e)}}}function Jt(l){let e,t,r="Connect to Database",d,o,n,c="Display name",a,s,f,i,p,b,_,D='<label class="text-sm font-medium text-gray-700 dark:text-gray-200">Connection String</label> <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4 cursor-pointer text-gray-400 transition-colors hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-200" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M18 10A8 8 0 11 2 10a8 8 0 0116 0zm-8-3a1 1 0 100-2 1 1 0 000 2zm-1 2a1 1 0 012 0v5a1 1 0 11-2 0v-5z" clip-rule="evenodd"></path></svg> <div class="pointer-events-none absolute left-0 top-full z-20 mt-2 w-96 rounded-md border border-gray-300 bg-white p-4 text-sm text-gray-700 opacity-0 shadow-lg transition-opacity duration-200 group-hover:pointer-events-auto group-hover:opacity-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100"><p class="mb-2 italic text-gray-500 dark:text-gray-400">Examples (for reference only):</p> <ul class="space-y-1"><li><strong>MySQL:</strong> <code class="block font-mono text-gray-600 dark:text-gray-300">mysql://user:pass@localhost:3306/database</code></li> <li><strong>PostgreSQL:</strong> <code class="block font-mono text-gray-600 dark:text-gray-300">postgresql://user:pass@localhost:5432/database</code></li> <li><strong>SQLite:</strong> <code class="block font-mono text-gray-600 dark:text-gray-300">sqlite:///path/to/database.db</code></li></ul></div>',C,y,E,M,L,P,w,T="Test Connection",I,x,V,B,z,X,ie,ne,ue,fe,te=l[9]&&We(),le=l[9]&&Re(),$=l[8]&&Qe(l),re=l[21]>1&&Ge(l),se=l[10]&&Ke(l);function pe(U,j){return j[0]&16&&(X=null),X==null&&(X=Object.keys(U[4]).length===0),X?Qt:Rt}let R=pe(l,[-1,-1]),ce=R(l),ee=l[12]&&tt(l);return{c(){e=g("div"),t=g("h1"),t.textContent=r,d=S(),o=g("div"),n=g("label"),n.textContent=c,a=S(),s=g("input"),i=S(),te&&te.c(),p=S(),b=g("div"),_=g("div"),_.innerHTML=D,C=S(),y=g("input"),M=S(),le&&le.c(),L=S(),P=g("div"),w=g("button"),w.textContent=T,I=S(),$&&$.c(),x=S(),re&&re.c(),V=S(),se&&se.c(),B=S(),z=g("div"),ce.c(),ie=S(),ee&&ee.c(),ne=we(),this.h()},l(U){e=v(U,"DIV",{class:!0});var j=k(e);t=v(j,"H1",{class:!0,"data-svelte-h":!0}),G(t)!=="svelte-121fstk"&&(t.textContent=r),d=N(j),o=v(j,"DIV",{});var _e=k(o);n=v(_e,"LABEL",{class:!0,"data-svelte-h":!0}),G(n)!=="svelte-uwf6tw"&&(n.textContent=c),a=N(_e),s=v(_e,"INPUT",{type:!0,placeholder:!0,class:!0}),i=N(_e),te&&te.l(_e),_e.forEach(m),p=N(j),b=v(j,"DIV",{});var A=k(b);_=v(A,"DIV",{class:!0,"data-svelte-h":!0}),G(_)!=="svelte-woiovi"&&(_.innerHTML=D),C=N(A),y=v(A,"INPUT",{type:!0,placeholder:!0,class:!0}),M=N(A),le&&le.l(A),A.forEach(m),L=N(j),P=v(j,"DIV",{class:!0});var F=k(P);w=v(F,"BUTTON",{class:!0,"data-svelte-h":!0}),G(w)!=="svelte-1vsiutm"&&(w.textContent=T),F.forEach(m),I=N(j),$&&$.l(j),x=N(j),re&&re.l(j),V=N(j),se&&se.l(j),j.forEach(m),B=N(U),z=v(U,"DIV",{});var oe=k(z);ce.l(oe),oe.forEach(m),ie=N(U),ee&&ee.l(U),ne=we(),this.h()},h(){h(t,"class","text-2xl font-semibold text-gray-800"),h(n,"class","mb-1 block text-sm font-medium text-gray-700"),h(s,"type","text"),h(s,"placeholder","Display name"),h(s,"class",f="w-full rounded-lg border p-3 shadow-sm transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 "+(l[9]?"border-red-500":"border-gray-300")),h(_,"class","group relative flex w-fit items-center"),h(y,"type","text"),h(y,"placeholder","example://user:pass@host:port/path"),h(y,"class",E="w-full rounded-lg border p-3 shadow-sm transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 "+(l[9]?"border-red-500":"border-gray-300")),h(w,"class","rounded-md border border-gray-300 bg-gray-100 px-4 py-2 font-medium text-gray-800 hover:bg-gray-200"),h(P,"class","mt-4 flex gap-4"),h(e,"class","mx-auto mt-10 max-w-xl space-y-6 rounded-2xl bg-white p-6 shadow-md")},m(U,j){H(U,e,j),u(e,t),u(e,d),u(e,o),u(o,n),u(o,a),u(o,s),ye(s,l[7]),u(o,i),te&&te.m(o,null),u(e,p),u(e,b),u(b,_),u(b,C),u(b,y),ye(y,l[5]),u(b,M),le&&le.m(b,null),u(e,L),u(e,P),u(P,w),u(e,I),$&&$.m(e,null),u(e,x),re&&re.m(e,null),u(e,V),se&&se.m(e,null),H(U,B,j),H(U,z,j),ce.m(z,null),H(U,ie,j),ee&&ee.m(U,j),H(U,ne,j),ue||(fe=[W(Wt,"keydown",l[32]),W(s,"input",l[38]),W(y,"input",l[39]),W(w,"click",l[30])],ue=!0)},p(U,j){j[0]&512&&f!==(f="w-full rounded-lg border p-3 shadow-sm transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 "+(U[9]?"border-red-500":"border-gray-300"))&&h(s,"class",f),j[0]&128&&s.value!==U[7]&&ye(s,U[7]),U[9]?te||(te=We(),te.c(),te.m(o,null)):te&&(te.d(1),te=null),j[0]&512&&E!==(E="w-full rounded-lg border p-3 shadow-sm transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 "+(U[9]?"border-red-500":"border-gray-300"))&&h(y,"class",E),j[0]&32&&y.value!==U[5]&&ye(y,U[5]),U[9]?le||(le=Re(),le.c(),le.m(b,null)):le&&(le.d(1),le=null),U[8]?$?$.p(U,j):($=Qe(U),$.c(),$.m(e,x)):$&&($.d(1),$=null),U[21]>1?re?re.p(U,j):(re=Ge(U),re.c(),re.m(e,V)):re&&(re.d(1),re=null),U[10]?se?se.p(U,j):(se=Ke(U),se.c(),se.m(e,null)):se&&(se.d(1),se=null),R===(R=pe(U,j))&&ce?ce.p(U,j):(ce.d(1),ce=R(U),ce&&(ce.c(),ce.m(z,null))),U[12]?ee?ee.p(U,j):(ee=tt(U),ee.c(),ee.m(ne.parentNode,ne)):ee&&(ee.d(1),ee=null)},i:ge,o:ge,d(U){U&&(m(e),m(B),m(z),m(ie),m(ne)),te&&te.d(),le&&le.d(),$&&$.d(),re&&re.d(),se&&se.d(),ce.d(),ee&&ee.d(U),ue=!1,ke(fe)}}}const He=5,Be=8;function lt(l){if(!l||typeof l!="string")return l||"";const e=/(?:\/|\\|)([a-zA-Z0-9_\/-]+\.(?:html|csv))/gi;return l.match(e)?l.replace(e,t=>`<a href="#" onclick="openHtmlViewer('${t.startsWith("/")?t.substring(1):t}'); return false;" class="text-blue-600 hover:underline">Data Summary</a>`):l}function $t(l,e,t){let r,d,o,n,c,a,s="",f="",i="",p=!1,b=!1,_=!1,D=!1,C=!1,y=[],E=!1,M="",L="File Viewer",P=!1,w=null,T=[],I=[],x="",V=1,B=[],z=1,X="";function ie(){z<n&&t(1,z+=1)}function ne(){z>1&&t(1,z-=1)}let ue={};function fe(O){t(19,ue[O]=!ue[O],ue)}function te(O,q){q?(t(17,I=[...I,O]),x=I.join(",")):t(17,I=I.filter(de=>de!==O))}function le(){V<a&&t(0,V++,V)}function $(){V>1&&t(0,V--,V)}async function re(){var O,q,de,he;if(p=!0,s.trim()==="")t(9,_=!0);else try{const J=await Te.post("/database_connection",{url:s,test_connection:p,displayName:i});t(35,T=J.data.tables),(O=J.data)!=null&&O.success?(window.alert("Test connection successful"),t(8,b=!0),t(10,D=!0)):window.alert("Connection failed: "+(((q=J.data)==null?void 0:q.message)||"Unknown error"))}catch(J){console.log("errrrrr:",J),window.alert(((he=(de=J.response)==null?void 0:de.data)==null?void 0:he.message)||"Connection test failed.")}finally{p=!1}}async function se(){var O,q;try{const de=await Te.get("/get_tables");t(34,y=de.data.data)}catch(de){console.log("errrror:",de),window.alert(((q=(O=de.response)==null?void 0:O.data)==null?void 0:q.message)||"Failed to fetch tables.")}finally{p=!1}}Ie(()=>{se()});async function pe(){var O,q,de,he;t(11,C=!0);try{const J=await Te.post("/database_connection",{url:s,test_connection:"false",tables:x,sqlQuery:f,displayName:i});(O=J.data)!=null&&O.success?(window.alert(J.data.data.message||"connected successfully"),window.location.reload(),t(11,C=!1)):window.alert("Connection failed: "+(((q=J.data)==null?void 0:q.message)||"Unknown error"))}catch(J){console.log("errrrrr:",J),t(11,C=!1),window.alert(((he=(de=J.response)==null?void 0:de.data)==null?void 0:he.message)||"Connection test failed.")}finally{p=!1,t(11,C=!1)}}let R;function ce(O){let q=O;!q.startsWith("html/")&&!q.startsWith("csv/")&&(q.endsWith(".html")?q=`html/${q}`:q.endsWith(".csv")&&(q=`csv/${q}`));const de=`${Ht}${q}`;t(14,L=q.split("/").pop()||"File Viewer"),t(15,P=!0),t(16,w=null),t(12,E=!0),t(13,M="");let he="";q.includes("#")&&([q,he]=q.split("#"),he="#"+he),fetch(de,{method:"GET",headers:{Authorization:`${Vt}`}}).then(J=>{if(!J.ok)throw new Error(`Failed to load content: ${J.status} ${J.statusText}`);return J.text()}).then(J=>{t(15,P=!1);const De=`
					<!DOCTYPE html>
					<html>
					<head>
						<base target="_self">
						<style>
							/* Ensure smooth scrolling for anchor navigation */
							html {
								scroll-behavior: smooth;
							}
							/* Prevent body overflow in iframe */
							body {
								overflow-x: hidden;
								margin: 0;
								padding: 10px;
							}
						</style>
						<script>
							// Wait for DOM to be fully loaded
							document.addEventListener('DOMContentLoaded', function() {
								// Fix for select elements and other form controls
								const selectElements = document.querySelectorAll('select');
								selectElements.forEach(select => {
									// Clone and replace to remove any existing event listeners
									const newSelect = select.cloneNode(true);
									select.parentNode.replaceChild(newSelect, select);
									
									// Add our own change handler
									newSelect.addEventListener('change', function(e) {
										// Execute any inline onchange handler if present
										const onChangeAttr = this.getAttribute('onchange');
										if (onChangeAttr) {
											// Create a function from the onchange attribute and call it
											// in the context of the select element
											try {
												const handler = new Function('event', onChangeAttr);
												handler.call(this, e);
											} catch (error) {
												console.error('Error executing onchange handler:', error);
											}
										}
									});
								});
								
								// Handle all form inputs
								const formInputs = document.querySelectorAll('input, textarea');
								formInputs.forEach(input => {
									// Clone and replace to ensure clean event handling
									const newInput = input.cloneNode(true);
									input.parentNode.replaceChild(newInput, input);
									
									// Re-attach events like onchange, oninput, onkeyup
									['change', 'input', 'keyup'].forEach(eventType => {
										const handlerAttr = newInput.getAttribute('on' + eventType);
										if (handlerAttr) {
											newInput.addEventListener(eventType, function(e) {
												try {
													const handler = new Function('event', handlerAttr);
													handler.call(this, e);
												} catch (error) {
													console.error(\`Error executing \${eventType} handler:\`, error);
												}
											});
										}
									});
								});
								
								// Handle form submissions
								const forms = document.querySelectorAll('form');
								forms.forEach(form => {
									form.addEventListener('submit', function(e) {
										e.preventDefault();
										
										// Execute any onsubmit handler if present
										const onSubmitAttr = this.getAttribute('onsubmit');
										if (onSubmitAttr) {
											try {
												// If onsubmit returns false, don't proceed
												const handler = new Function('event', onSubmitAttr);
												const result = handler.call(this, e);
												if (result === false) return;
											} catch (error) {
												console.error('Error executing onsubmit handler:', error);
											}
										}
										
										console.log('Form submission handled in iframe');
									});
								});
							});
							
							// Handle all link clicks to prevent breaking out of iframe
							document.addEventListener('click', function(e) {
								const target = e.target.closest('a');
								if (target && target.getAttribute('href')) {
									const href = target.getAttribute('href');
									
									// Handle anchor links
									if (href.startsWith('#')) {
										e.preventDefault();
										const element = document.querySelector(href);
										if (element) {
											element.scrollIntoView({
												behavior: 'smooth',
												block: 'start'
											});
										}
									}
									// Handle relative links
									else if (!href.startsWith('http') && !href.startsWith('//')) {
										e.preventDefault();
										// Notify parent that we want to navigate
										window.parent.postMessage({
											type: 'navigate',
											path: href
										}, '*');
									}
								}
							}, true);
							
							// Set up a MutationObserver to handle dynamically added elements
							const observer = new MutationObserver(function(mutations) {
								mutations.forEach(function(mutation) {
									if (mutation.addedNodes && mutation.addedNodes.length > 0) {
										// Check for newly added form elements
										mutation.addedNodes.forEach(function(node) {
											if (node.querySelectorAll) {
												// Process any new select elements
												const newSelects = node.querySelectorAll('select');
												newSelects.forEach(select => {
													select.addEventListener('change', function(e) {
														const onChangeAttr = this.getAttribute('onchange');
														if (onChangeAttr) {
															try {
																const handler = new Function('event', onChangeAttr);
																handler.call(this, e);
															} catch (error) {
																console.error('Error executing onchange handler:', error);
															}
														}
													});
												});
											}
										});
									}
								});
							});
							
							// Start observing document body for changes
							observer.observe(document.body, { 
								childList: true, 
								subtree: true 
							});
							
							// Notify parent frame that we're ready
							window.parent.postMessage({ type: 'iframeReady' }, '*');
						<\/script>
					</head>
					<body>${J}</body>
					</html>`;t(13,M=De),he&&R&&setTimeout(()=>{const Oe=R.contentWindow;Oe&&Oe.postMessage({type:"scrollToAnchor",anchor:he},"*")},300)}).catch(J=>{console.error("Error fetching HTML file:",J),t(15,P=!1),t(16,w=J.message)})}function ee(O){!O.data||typeof O.data!="object"||(O.data.type==="navigate"?ce(O.data.path):O.data.type==="iframeReady"&&console.log("Iframe content fully loaded and initialized"))}Ie(()=>(window.addEventListener("message",ee),window.openHtmlViewer=ce,console.log("Checking for HTML/CSV files in summaries..."),document.querySelectorAll(".summary .truncate-text").forEach((q,de)=>{const he=q.innerHTML;(he.includes(".html")||he.includes(".csv"))&&console.log(`Found file reference in summary ${de}:`,he)}),()=>{window.removeEventListener("message",ee)}));function U(O){O.key==="Escape"&&E&&j()}function j(){t(12,E=!1),setTimeout(()=>{t(13,M=""),t(16,w=null)},300)}function _e(O){Ct.call(this,l,O)}function A(){i=this.value,t(7,i)}function F(){s=this.value,t(5,s)}function oe(){f=this.value,t(6,f)}const Z=(O,q)=>te(O,q.target.checked);function K(){X=this.value,t(2,X)}const me=O=>fe(O);function be(O){Tt[O?"unshift":"push"](()=>{R=O,t(20,R)})}return l.$$.update=()=>{l.$$.dirty[1]&8&&t(4,r=y.reduce((O,q)=>(O[q.directory]||(O[q.directory]=[]),O[q.directory].push(q),O),{})),l.$$.dirty[0]&16&&t(36,d=Object.keys(r).sort((O,q)=>{var J,De;const de=new Date(((J=r[O][0])==null?void 0:J.created_at)||0);return new Date(((De=r[q][0])==null?void 0:De.created_at)||0)-de})),l.$$.dirty[0]&4|l.$$.dirty[1]&32&&t(3,o=d.filter(O=>O.toLowerCase().includes(X.toLowerCase()))),l.$$.dirty[0]&8&&t(22,n=Math.ceil(o.length/Be)),l.$$.dirty[0]&4&&X&&t(1,z=1),l.$$.dirty[0]&10&&t(23,c=o.slice((z-1)*Be,z*Be)),l.$$.dirty[0]&1|l.$$.dirty[1]&16&&t(18,B=T.slice((V-1)*He,V*He)),l.$$.dirty[1]&16&&t(21,a=Math.ceil(T.length/He))},[V,z,X,o,r,s,f,i,b,_,D,C,E,M,L,P,w,I,B,ue,R,a,n,c,ie,ne,fe,te,le,$,re,pe,U,j,y,T,d,_e,A,F,oe,Z,K,me,be]}class el extends Pt{constructor(e){super(),xt(this,e,$t,Jt,kt,{},null,[-1,-1])}}const tl=typeof window<"u"&&localStorage.getItem("activeComponent")||"fileUploader",Ae=jt(tl);typeof window<"u"&&Ae.subscribe(l=>{localStorage.setItem("activeComponent",l)});const{window:ll}=Mt;function rt(l,e,t){const r=l.slice();return r[54]=e[t],r[56]=t,r}function nt(l,e,t){const r=l.slice();return r[57]=e[t],r[59]=t,r}function st(l,e,t){const r=l.slice();return r[54]=e[t],r[56]=t,r}function ot(l,e,t){const r=l.slice();return r[61]=e[t],r}function rl(l){let e,t,r,d,o,n,c,a,s,f=l[5].toLocaleString()+"",i,p,b,_='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="20" height="20"><path d="M105.1 202.6c7.7-21.8 20.2-42.3 37.8-59.8c62.5-62.5 163.8-62.5 226.3 0L386.3 160 352 160c-17.7 0-32 14.3-32 32s14.3 32 32 32l111.5 0c0 0 0 0 0 0l.4 0c17.7 0 32-14.3 32-32l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 35.2L414.4 97.6c-87.5-87.5-229.3-87.5-316.8 0C73.2 122 55.6 150.7 44.8 181.4c-5.9 16.7 2.9 34.9 19.5 40.8s34.9-2.9 40.8-19.5zM39 289.3c-5 1.5-9.8 4.2-13.7 8.2c-4 4-6.7 8.8-8.1 14c-.3 1.2-.6 2.5-.8 3.8c-.3 1.7-.4 3.4-.4 5.1L16 432c0 17.7 14.3 32 32 32s32-14.3 32-32l0-35.1 17.6 17.5c0 0 0 0 0 0c87.5 87.4 229.3 87.4 316.7 0c24.4-24.4 42.1-53.1 52.9-83.8c5.9-16.7-2.9-34.9-19.5-40.8s-34.9 2.9-40.8 19.5c-7.7 21.8-20.2 42.3-37.8 59.8c-62.5 62.5-163.8 62.5-226.3 0l-.1-.1L125.6 352l34.4 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L48.4 288c-1.6 0-3.2 .1-4.8 .3s-3.1 .5-4.6 1z"></path></svg>',D,C,y,E,M;e=new zt({});let L=ve(yt(l[9],l[6],Ee)),P=[];for(let T=0;T<L.length;T+=1)P[T]=ht(nt(l,L,T));let w=l[9].length>1&&mt(l);return{c(){At(e.$$.fragment),t=S(),r=g("div"),d=g("div"),o=g("input"),n=S(),c=g("div"),a=g("p"),s=Q("Number of Files: "),i=Q(f),p=S(),b=g("p"),b.innerHTML=_,D=S();for(let T=0;T<P.length;T+=1)P[T].c();C=S(),w&&w.c(),this.h()},l(T){It(e.$$.fragment,T),t=N(T),r=v(T,"DIV",{class:!0});var I=k(r);d=v(I,"DIV",{class:!0});var x=k(d);o=v(x,"INPUT",{type:!0,placeholder:!0,class:!0}),x.forEach(m),n=N(I),c=v(I,"DIV",{class:!0});var V=k(c);a=v(V,"P",{class:!0});var B=k(a);s=Y(B,"Number of Files: "),i=Y(B,f),B.forEach(m),p=N(V),b=v(V,"P",{class:!0,"data-svelte-h":!0}),G(b)!=="svelte-pjs8p6"&&(b.innerHTML=_),V.forEach(m),D=N(I);for(let z=0;z<P.length;z+=1)P[z].l(I);C=N(I),w&&w.l(I),I.forEach(m),this.h()},h(){h(o,"type","text"),h(o,"placeholder","Search directories..."),h(o,"class","dark-input w-full rounded border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"),h(d,"class","mt-5"),h(a,"class","my-3"),h(b,"class","refresh svelte-ttfemr"),h(c,"class","items svelte-ttfemr"),h(r,"class","me-4")},m(T,I){St(e,T,I),H(T,t,I),H(T,r,I),u(r,d),u(d,o),ye(o,l[8]),u(r,n),u(r,c),u(c,a),u(a,s),u(a,i),u(c,p),u(c,b),u(r,D);for(let x=0;x<P.length;x+=1)P[x]&&P[x].m(r,null);u(r,C),w&&w.m(r,null),y=!0,E||(M=[W(o,"input",l[24]),W(o,"input",l[16]),W(b,"click",l[25])],E=!0)},p(T,I){if(I[0]&256&&o.value!==T[8]&&ye(o,T[8]),(!y||I[0]&32)&&f!==(f=T[5].toLocaleString()+"")&&ae(i,f),I[0]&705216){L=ve(yt(T[9],T[6],Ee));let x;for(x=0;x<L.length;x+=1){const V=nt(T,L,x);P[x]?P[x].p(V,I):(P[x]=ht(V),P[x].c(),P[x].m(r,C))}for(;x<P.length;x+=1)P[x].d(1);P.length=L.length}T[9].length>1?w?w.p(T,I):(w=mt(T),w.c(),w.m(r,null)):w&&(w.d(1),w=null)},i(T){y||(Me(e.$$.fragment,T),y=!0)},o(T){Ne(e.$$.fragment,T),y=!1},d(T){T&&(m(t),m(r)),Nt(e,T),Ce(P,T),w&&w.d(),E=!1,ke(M)}}}function nl(l){let e,t;return e=new el({}),{c(){At(e.$$.fragment)},l(r){It(e.$$.fragment,r)},m(r,d){St(e,r,d),t=!0},p:ge,i(r){t||(Me(e.$$.fragment,r),t=!0)},o(r){Ne(e.$$.fragment,r),t=!1},d(r){Nt(e,r)}}}function sl(l){let e,t,r,d,o='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="20" height="20"><path d="M0 96C0 60.7 28.7 32 64 32l132.1 0c19.1 0 37.4 7.6 50.9 21.1L289.9 96 448 96c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM64 80c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l384 0c8.8 0 16-7.2 16-16l0-256c0-8.8-7.2-16-16-16l-161.4 0c-10.6 0-20.8-4.2-28.3-11.7L213.1 87c-4.5-4.5-10.6-7-17-7L64 80z"></path></svg>',n,c=(l[57].directory||"No Directory")+"",a,s,f;function i(){return l[28](l[57])}return{c(){e=g("div"),t=g("h2"),r=g("span"),d=g("span"),d.innerHTML=o,n=S(),a=Q(c),this.h()},l(p){e=v(p,"DIV",{class:!0});var b=k(e);t=v(b,"H2",{class:!0});var _=k(t);r=v(_,"SPAN",{class:!0});var D=k(r);d=v(D,"SPAN",{class:!0,"data-svelte-h":!0}),G(d)!=="svelte-zi3uqp"&&(d.innerHTML=o),n=N(D),a=Y(D,c),D.forEach(m),_.forEach(m),b.forEach(m),this.h()},h(){h(d,"class","mx-3"),h(r,"class","flex"),h(t,"class","dir svelte-ttfemr"),h(e,"class","mb-2 flex items-center pb-4")},m(p,b){H(p,e,b),u(e,t),u(t,r),u(r,d),u(r,n),u(r,a),s||(f=W(t,"click",i),s=!0)},p(p,b){l=p,b[0]&576&&c!==(c=(l[57].directory||"No Directory")+"")&&ae(a,c)},d(p){p&&m(e),s=!1,f()}}}function ol(l){let e,t,r,d,o='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="20" height="20"><path d="M0 96C0 60.7 28.7 32 64 32l132.1 0c19.1 0 37.4 7.6 50.9 21.1L289.9 96 448 96c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64L64 480c-35.3 0-64-28.7-64-64L0 96zM64 80c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l384 0c8.8 0 16-7.2 16-16l0-256c0-8.8-7.2-16-16-16l-161.4 0c-10.6 0-20.8-4.2-28.3-11.7L213.1 87c-4.5-4.5-10.6-7-17-7L64 80z"></path></svg>',n,c=(l[57].directory||"No Directory")+"",a,s,f,i,p,b=l[57].children[0].isEnabled==="active"||l[57].children[0].isEnabled===void 0?"Active":"Inactive",_,D,C,y,E,M;function L(){return l[26](l[57])}function P(I,x){return I[7][I[57].directory]?il:al}let w=P(l),T=w(l);return{c(){e=g("div"),t=g("h2"),r=g("span"),d=g("span"),d.innerHTML=o,n=S(),a=Q(c),s=S(),f=g("div"),i=g("div"),p=g("span"),_=Q(b),D=S(),C=g("label"),T.c(),this.h()},l(I){e=v(I,"DIV",{class:!0});var x=k(e);t=v(x,"H2",{class:!0});var V=k(t);r=v(V,"SPAN",{class:!0});var B=k(r);d=v(B,"SPAN",{class:!0,"data-svelte-h":!0}),G(d)!=="svelte-zi3uqp"&&(d.innerHTML=o),n=N(B),a=Y(B,c),B.forEach(m),V.forEach(m),s=N(x),f=v(x,"DIV",{style:!0});var z=k(f);i=v(z,"DIV",{class:!0});var X=k(i);p=v(X,"SPAN",{class:!0});var ie=k(p);_=Y(ie,b),ie.forEach(m),D=N(X),C=v(X,"LABEL",{class:!0,title:!0});var ne=k(C);T.l(ne),ne.forEach(m),X.forEach(m),z.forEach(m),x.forEach(m),this.h()},h(){h(d,"class","mx-3"),h(r,"class","flex"),h(t,"class","dir svelte-ttfemr"),h(p,"class","toggle-label svelte-ttfemr"),h(C,"class","toggle-switch svelte-ttfemr"),h(C,"title",y=l[57].children[0].isEnabled==="active"||l[57].children[0].isEnabled===void 0?"Click to disable this document set":"Click to enable this document set"),h(i,"class","toggle-container svelte-ttfemr"),Se(f,"margin-left","10px"),h(e,"class","flex items-center justify-between")},m(I,x){H(I,e,x),u(e,t),u(t,r),u(r,d),u(r,n),u(r,a),u(e,s),u(e,f),u(f,i),u(i,p),u(p,_),u(i,D),u(i,C),T.m(C,null),E||(M=W(t,"click",L),E=!0)},p(I,x){l=I,x[0]&576&&c!==(c=(l[57].directory||"No Directory")+"")&&ae(a,c),x[0]&576&&b!==(b=l[57].children[0].isEnabled==="active"||l[57].children[0].isEnabled===void 0?"Active":"Inactive")&&ae(_,b),w===(w=P(l))&&T?T.p(l,x):(T.d(1),T=w(l),T&&(T.c(),T.m(C,null))),x[0]&576&&y!==(y=l[57].children[0].isEnabled==="active"||l[57].children[0].isEnabled===void 0?"Click to disable this document set":"Click to enable this document set")&&h(C,"title",y)},d(I){I&&m(e),T.d(),E=!1,M()}}}function al(l){let e,t,r,d,o,n;function c(...a){return l[27](l[59],l[57],...a)}return{c(){e=g("input"),r=S(),d=g("span"),this.h()},l(a){e=v(a,"INPUT",{type:!0,class:!0}),r=N(a),d=v(a,"SPAN",{class:!0}),k(d).forEach(m),this.h()},h(){h(e,"type","checkbox"),e.checked=t=l[57].children[0].isEnabled==="active"||l[57].children[0].isEnabled===void 0,h(e,"class","svelte-ttfemr"),h(d,"class","toggle-slider svelte-ttfemr")},m(a,s){H(a,e,s),H(a,r,s),H(a,d,s),o||(n=W(e,"change",c),o=!0)},p(a,s){l=a,s[0]&576&&t!==(t=l[57].children[0].isEnabled==="active"||l[57].children[0].isEnabled===void 0)&&(e.checked=t)},d(a){a&&(m(e),m(r),m(d)),o=!1,n()}}}function il(l){let e;return{c(){e=g("div"),this.h()},l(t){e=v(t,"DIV",{class:!0}),k(e).forEach(m),this.h()},h(){h(e,"class","toggle-loader svelte-ttfemr")},m(t,r){H(t,e,r)},p:ge,d(t){t&&m(e)}}}function at(l){let e,t,r='<tr><th class="doc-name px-6 py-3 svelte-ttfemr">Document Name</th> <th class="summary px-6 py-3 svelte-ttfemr">Summary</th> <th class="px-6 py-3 svelte-ttfemr">Upload Date</th> <th class="px-6 py-3 svelte-ttfemr">Uploaded by</th> <th class="px-6 py-3 svelte-ttfemr">Status</th> <th class="action px-6 py-3 svelte-ttfemr">Action</th></tr>',d,o,n,c,a=ve(bt(l[57].children,l[57].currentPage,l[57].itemsPerPage)),s=[];for(let i=0;i<a.length;i+=1)s[i]=it(ot(l,a,i));let f=l[57].children.length>5&&ct(l);return{c(){e=g("table"),t=g("thead"),t.innerHTML=r,d=S(),o=g("tbody");for(let i=0;i<s.length;i+=1)s[i].c();n=S(),f&&f.c(),c=we(),this.h()},l(i){e=v(i,"TABLE",{class:!0});var p=k(e);t=v(p,"THEAD",{"data-svelte-h":!0}),G(t)!=="svelte-f2mb4m"&&(t.innerHTML=r),d=N(p),o=v(p,"TBODY",{});var b=k(o);for(let _=0;_<s.length;_+=1)s[_].l(b);b.forEach(m),p.forEach(m),n=N(i),f&&f.l(i),c=we(),this.h()},h(){h(e,"class","inner-table mt-3 svelte-ttfemr")},m(i,p){H(i,e,p),u(e,t),u(e,d),u(e,o);for(let b=0;b<s.length;b+=1)s[b]&&s[b].m(o,null);H(i,n,p),f&&f.m(i,p),H(i,c,p)},p(i,p){if(p[0]&33344){a=ve(bt(i[57].children,i[57].currentPage,i[57].itemsPerPage));let b;for(b=0;b<a.length;b+=1){const _=ot(i,a,b);s[b]?s[b].p(_,p):(s[b]=it(_),s[b].c(),s[b].m(o,null))}for(;b<s.length;b+=1)s[b].d(1);s.length=a.length}i[57].children.length>5?f?f.p(i,p):(f=ct(i),f.c(),f.m(c.parentNode,c)):f&&(f.d(1),f=null)},d(i){i&&(m(e),m(n),m(c)),Ce(s,i),f&&f.d(i)}}}function it(l){let e,t,r,d=l[61].doc_name+"",o,n,c,a,s,f=wt(Fe.parse(l[61].summary))+"",i,p,b,_=l[61].upload_date+"",D,C,y,E,M=l[61].email+"",L,P,w,T,I=l[61].state+"",x,V,B,z=l[61].message+"",X,ie,ne,ue,fe,te='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="16" height="16" fill="currentColor"><path d="M135.2 17.7L128 32 32 32C14.3 32 0 46.3 0 64S14.3 96 32 96l384 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0-7.2-14.3C307.4 6.8 296.3 0 284.2 0L163.8 0c-12.1 0-23.2 6.8-28.6 17.7zM416 128L32 128 53.2 467c1.6 25.3 22.6 45 47.9 45l245.8 0c25.3 0 46.3-19.7 47.9-45L416 128z"></path></svg>',le,$,re;function se(){return l[29](l[61],l[57])}return{c(){e=g("tr"),t=g("td"),r=g("div"),o=Q(d),n=S(),c=g("td"),a=g("div"),s=new Dt(!1),i=S(),p=g("td"),b=g("div"),D=Q(_),C=S(),y=g("td"),E=g("div"),L=Q(M),P=S(),w=g("td"),T=g("span"),x=Q(I),V=S(),B=g("span"),X=Q(z),ie=S(),ne=g("td"),ue=g("div"),fe=g("span"),fe.innerHTML=te,le=S(),this.h()},l(pe){e=v(pe,"TR",{});var R=k(e);t=v(R,"TD",{class:!0});var ce=k(t);r=v(ce,"DIV",{class:!0});var ee=k(r);o=Y(ee,d),ee.forEach(m),ce.forEach(m),n=N(R),c=v(R,"TD",{class:!0});var U=k(c);a=v(U,"DIV",{class:!0});var j=k(a);s=Lt(j,!1),j.forEach(m),U.forEach(m),i=N(R),p=v(R,"TD",{class:!0});var _e=k(p);b=v(_e,"DIV",{class:!0});var A=k(b);D=Y(A,_),A.forEach(m),_e.forEach(m),C=N(R),y=v(R,"TD",{class:!0});var F=k(y);E=v(F,"DIV",{class:!0});var oe=k(E);L=Y(oe,M),oe.forEach(m),F.forEach(m),P=N(R),w=v(R,"TD",{class:!0});var Z=k(w);T=v(Z,"SPAN",{});var K=k(T);x=Y(K,I),K.forEach(m),V=N(Z),B=v(Z,"SPAN",{class:!0});var me=k(B);X=Y(me,z),me.forEach(m),Z.forEach(m),ie=N(R),ne=v(R,"TD",{class:!0,title:!0});var be=k(ne);ue=v(be,"DIV",{class:!0});var O=k(ue);fe=v(O,"SPAN",{style:!0,"data-svelte-h":!0}),G(fe)!=="svelte-i6efga"&&(fe.innerHTML=te),O.forEach(m),be.forEach(m),le=N(R),R.forEach(m),this.h()},h(){h(r,"class","cursor truncate-text svelte-ttfemr"),h(t,"class","wrap-text doc-name px-6 py-4 svelte-ttfemr"),s.a=null,h(a,"class","cursor truncate-text svelte-ttfemr"),h(c,"class","wrap-text summary px-6 py-4 svelte-ttfemr"),h(b,"class","cursor truncate-text svelte-ttfemr"),h(p,"class","wrap-text px-6 py-4 svelte-ttfemr"),h(E,"class","cursor truncate-text svelte-ttfemr"),h(y,"class","wrap-text px-6 py-4 svelte-ttfemr"),h(B,"class","absolute left-1/2 top-full mt-1 hidden -translate-x-1/2 transform rounded-lg bg-gray-500 px-3 py-1 text-xs text-white opacity-0 transition-opacity delay-150 duration-300 ease-in-out group-hover:block group-hover:opacity-100"),h(w,"class","group relative px-6 py-4 svelte-ttfemr"),Se(fe,"color","red"),h(ue,"class","cursor truncate-text svelte-ttfemr"),h(ne,"class","action px-6 py-4 svelte-ttfemr"),h(ne,"title","delete")},m(pe,R){H(pe,e,R),u(e,t),u(t,r),u(r,o),u(e,n),u(e,c),u(c,a),s.m(f,a),u(e,i),u(e,p),u(p,b),u(b,D),u(e,C),u(e,y),u(y,E),u(E,L),u(e,P),u(e,w),u(w,T),u(T,x),u(w,V),u(w,B),u(B,X),u(e,ie),u(e,ne),u(ne,ue),u(ue,fe),u(e,le),$||(re=[W(r,"click",Le),W(a,"click",Le),W(b,"click",Le),W(E,"click",Le),W(fe,"click",se)],$=!0)},p(pe,R){l=pe,R[0]&576&&d!==(d=l[61].doc_name+"")&&ae(o,d),R[0]&576&&f!==(f=wt(Fe.parse(l[61].summary))+"")&&s.p(f),R[0]&576&&_!==(_=l[61].upload_date+"")&&ae(D,_),R[0]&576&&M!==(M=l[61].email+"")&&ae(L,M),R[0]&576&&I!==(I=l[61].state+"")&&ae(x,I),R[0]&576&&z!==(z=l[61].message+"")&&ae(X,z)},d(pe){pe&&m(e),$=!1,ke(re)}}}function ct(l){let e,t,r,d=l[57].currentPage<Pe(l[57].children,l[57].itemsPerPage),o=l[57].currentPage>1&&dt(l),n=ve(Array(Pe(l[57].children,l[57].itemsPerPage))),c=[];for(let s=0;s<n.length;s+=1)c[s]=ut(st(l,n,s));let a=d&&ft(l);return{c(){e=g("div"),o&&o.c(),t=S();for(let s=0;s<c.length;s+=1)c[s].c();r=S(),a&&a.c(),this.h()},l(s){e=v(s,"DIV",{class:!0});var f=k(e);o&&o.l(f),t=N(f);for(let i=0;i<c.length;i+=1)c[i].l(f);r=N(f),a&&a.l(f),f.forEach(m),this.h()},h(){h(e,"class","pagination mt-3 flex items-center justify-center gap-2 text-center svelte-ttfemr")},m(s,f){H(s,e,f),o&&o.m(e,null),u(e,t);for(let i=0;i<c.length;i+=1)c[i]&&c[i].m(e,null);u(e,r),a&&a.m(e,null)},p(s,f){if(s[57].currentPage>1?o?o.p(s,f):(o=dt(s),o.c(),o.m(e,t)):o&&(o.d(1),o=null),f[0]&131648){n=ve(Array(Pe(s[57].children,s[57].itemsPerPage)));let i;for(i=0;i<n.length;i+=1){const p=st(s,n,i);c[i]?c[i].p(p,f):(c[i]=ut(p),c[i].c(),c[i].m(e,r))}for(;i<c.length;i+=1)c[i].d(1);c.length=n.length}f[0]&576&&(d=s[57].currentPage<Pe(s[57].children,s[57].itemsPerPage)),d?a?a.p(s,f):(a=ft(s),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},d(s){s&&m(e),o&&o.d(),Ce(c,s),a&&a.d()}}}function dt(l){let e,t="Previous",r,d;function o(){return l[30](l[57])}return{c(){e=g("button"),e.textContent=t,this.h()},l(n){e=v(n,"BUTTON",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-12m8zlk"&&(e.textContent=t),this.h()},h(){h(e,"class","flex h-8 items-center justify-center rounded-lg border border-gray-300 bg-white px-3 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white")},m(n,c){H(n,e,c),r||(d=W(e,"click",o),r=!0)},p(n,c){l=n},d(n){n&&m(e),r=!1,d()}}}function ut(l){let e,t=l[56]+1+"",r,d,o;function n(){return l[31](l[57],l[56])}return{c(){e=g("button"),r=Q(t),this.h()},l(c){e=v(c,"BUTTON",{class:!0});var a=k(e);r=Y(a,t),a.forEach(m),this.h()},h(){h(e,"class","flex h-8 items-center justify-center rounded-lg border border-gray-300 bg-white px-3 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white")},m(c,a){H(c,e,a),u(e,r),d||(o=W(e,"click",n),d=!0)},p(c,a){l=c},d(c){c&&m(e),d=!1,o()}}}function ft(l){let e,t="Next",r,d;function o(){return l[32](l[57])}return{c(){e=g("button"),e.textContent=t,this.h()},l(n){e=v(n,"BUTTON",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-12b3iz6"&&(e.textContent=t),this.h()},h(){h(e,"class","flex h-8 items-center justify-center rounded-lg border border-gray-300 bg-white px-3 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white")},m(n,c){H(n,e,c),r||(d=W(e,"click",o),r=!0)},p(n,c){l=n},d(n){n&&m(e),r=!1,d()}}}function ht(l){let e,t;function r(c,a){return c[57].collapsed?ol:sl}let d=r(l),o=d(l),n=!l[57].collapsed&&at(l);return{c(){e=g("div"),o.c(),t=S(),n&&n.c(),this.h()},l(c){e=v(c,"DIV",{class:!0});var a=k(e);o.l(a),t=N(a),n&&n.l(a),a.forEach(m),this.h()},h(){h(e,"class","directory border p-3")},m(c,a){H(c,e,a),o.m(e,null),u(e,t),n&&n.m(e,null)},p(c,a){d===(d=r(c))&&o?o.p(c,a):(o.d(1),o=d(c),o&&(o.c(),o.m(e,t))),c[57].collapsed?n&&(n.d(1),n=null):n?n.p(c,a):(n=at(c),n.c(),n.m(e,null))},d(c){c&&m(e),o.d(),n&&n.d()}}}function mt(l){let e,t,r,d=l[6]<xe(l[9],Ee),o=l[6]>1&&pt(l),n=ve(Array(xe(l[9],Ee))),c=[];for(let s=0;s<n.length;s+=1)c[s]=gt(rt(l,n,s));let a=d&&vt(l);return{c(){e=g("div"),o&&o.c(),t=S();for(let s=0;s<c.length;s+=1)c[s].c();r=S(),a&&a.c(),this.h()},l(s){e=v(s,"DIV",{class:!0});var f=k(e);o&&o.l(f),t=N(f);for(let i=0;i<c.length;i+=1)c[i].l(f);r=N(f),a&&a.l(f),f.forEach(m),this.h()},h(){h(e,"class","pagination mt-3 flex items-center justify-center gap-2 text-center svelte-ttfemr")},m(s,f){H(s,e,f),o&&o.m(e,null),u(e,t);for(let i=0;i<c.length;i+=1)c[i]&&c[i].m(e,null);u(e,r),a&&a.m(e,null)},p(s,f){if(s[6]>1?o?o.p(s,f):(o=pt(s),o.c(),o.m(e,t)):o&&(o.d(1),o=null),f[0]&262656){n=ve(Array(xe(s[9],Ee)));let i;for(i=0;i<n.length;i+=1){const p=rt(s,n,i);c[i]?c[i].p(p,f):(c[i]=gt(p),c[i].c(),c[i].m(e,r))}for(;i<c.length;i+=1)c[i].d(1);c.length=n.length}f[0]&576&&(d=s[6]<xe(s[9],Ee)),d?a?a.p(s,f):(a=vt(s),a.c(),a.m(e,null)):a&&(a.d(1),a=null)},d(s){s&&m(e),o&&o.d(),Ce(c,s),a&&a.d()}}}function pt(l){let e,t="Previous",r,d;return{c(){e=g("button"),e.textContent=t,this.h()},l(o){e=v(o,"BUTTON",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-vjczoy"&&(e.textContent=t),this.h()},h(){h(e,"class","flex h-8 items-center justify-center rounded-lg border border-gray-300 bg-white px-3 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white")},m(o,n){H(o,e,n),r||(d=W(e,"click",l[33]),r=!0)},p:ge,d(o){o&&m(e),r=!1,d()}}}function gt(l){let e,t=l[56]+1+"",r,d,o;function n(){return l[34](l[56])}return{c(){e=g("button"),r=Q(t),this.h()},l(c){e=v(c,"BUTTON",{class:!0});var a=k(e);r=Y(a,t),a.forEach(m),this.h()},h(){h(e,"class","flex h-8 items-center justify-center rounded-lg border border-gray-300 bg-white px-3 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white")},m(c,a){H(c,e,a),u(e,r),d||(o=W(e,"click",n),d=!0)},p(c,a){l=c},d(c){c&&m(e),d=!1,o()}}}function vt(l){let e,t="Next",r,d;return{c(){e=g("button"),e.textContent=t,this.h()},l(o){e=v(o,"BUTTON",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-1rbdr8p"&&(e.textContent=t),this.h()},h(){h(e,"class","flex h-8 items-center justify-center rounded-lg border border-gray-300 bg-white px-3 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white")},m(o,n){H(o,e,n),r||(d=W(e,"click",l[35]),r=!0)},p:ge,d(o){o&&m(e),r=!1,d()}}}function _t(l){let e,t,r,d,o,n,c="×",a,s,f,i;function p(D,C){return D[3]?ul:D[4]?dl:cl}let b=p(l),_=b(l);return{c(){e=g("div"),t=g("div"),r=g("div"),d=g("h2"),o=S(),n=g("button"),n.textContent=c,a=S(),s=g("div"),_.c(),this.h()},l(D){e=v(D,"DIV",{class:!0,role:!0,"aria-modal":!0});var C=k(e);t=v(C,"DIV",{class:!0});var y=k(t);r=v(y,"DIV",{class:!0});var E=k(r);d=v(E,"H2",{}),k(d).forEach(m),o=N(E),n=v(E,"BUTTON",{class:!0,"aria-label":!0,"data-svelte-h":!0}),G(n)!=="svelte-s5zcta"&&(n.textContent=c),E.forEach(m),a=N(y),s=v(y,"DIV",{class:!0});var M=k(s);_.l(M),M.forEach(m),y.forEach(m),C.forEach(m),this.h()},h(){h(n,"class","close-button svelte-ttfemr"),h(n,"aria-label","Close modal"),h(r,"class","modal-header svelte-ttfemr"),h(s,"class","modal-content svelte-ttfemr"),h(t,"class","modal-container svelte-ttfemr"),h(e,"class","modal-overlay svelte-ttfemr"),h(e,"role","dialog"),h(e,"aria-modal","true")},m(D,C){H(D,e,C),u(e,t),u(t,r),u(r,d),u(r,o),u(r,n),u(t,a),u(t,s),_.m(s,null),f||(i=[W(n,"click",l[21]),W(t,"click",Et(l[23])),W(e,"click",l[21])],f=!0)},p(D,C){b===(b=p(D))&&_?_.p(D,C):(_.d(1),_=b(D),_&&(_.c(),_.m(s,null)))},d(D){D&&m(e),_.d(),f=!1,ke(i)}}}function cl(l){let e;return{c(){e=g("iframe"),this.h()},l(t){e=v(t,"IFRAME",{class:!0,title:!0,srcdoc:!0,sandbox:!0}),k(e).forEach(m),this.h()},h(){h(e,"class","content-iframe svelte-ttfemr"),h(e,"title",l[2]),h(e,"srcdoc",l[1]),h(e,"sandbox","allow-same-origin allow-scripts allow-forms")},m(t,r){H(t,e,r),l[36](e)},p(t,r){r[0]&4&&h(e,"title",t[2]),r[0]&2&&h(e,"srcdoc",t[1])},d(t){t&&m(e),l[36](null)}}}function dl(l){let e,t,r="Error Loading Content",d,o,n="Sorry, we couldn't load the requested file.",c,a,s;return{c(){e=g("div"),t=g("h4"),t.textContent=r,d=S(),o=g("p"),o.textContent=n,c=S(),a=g("div"),s=Q(l[4]),this.h()},l(f){e=v(f,"DIV",{class:!0});var i=k(e);t=v(i,"H4",{"data-svelte-h":!0}),G(t)!=="svelte-1cp7uz"&&(t.textContent=r),d=N(i),o=v(i,"P",{"data-svelte-h":!0}),G(o)!=="svelte-xb86a4"&&(o.textContent=n),c=N(i),a=v(i,"DIV",{class:!0});var p=k(a);s=Y(p,l[4]),p.forEach(m),i.forEach(m),this.h()},h(){h(a,"class","error-details svelte-ttfemr"),h(e,"class","modal-error svelte-ttfemr")},m(f,i){H(f,e,i),u(e,t),u(e,d),u(e,o),u(e,c),u(e,a),u(a,s)},p(f,i){i[0]&16&&ae(s,f[4])},d(f){f&&m(e)}}}function ul(l){let e,t='<div class="spinner svelte-ttfemr"></div> <p>Loading content...</p>';return{c(){e=g("div"),e.innerHTML=t,this.h()},l(r){e=v(r,"DIV",{class:!0,"data-svelte-h":!0}),G(e)!=="svelte-61c640"&&(e.innerHTML=t),this.h()},h(){h(e,"class","modal-loading svelte-ttfemr")},m(r,d){H(r,e,d)},p:ge,d(r){r&&m(e)}}}function fl(l){let e,t,r,d="Upload Files",o,n,c="Connect Database",a,s,f,i,p,b,_,D;const C=[nl,rl],y=[];function E(L,P){return L[11]==="dataBases"?0:L[11]==="fileUploader"?1:-1}~(s=E(l))&&(f=y[s]=C[s](l));let M=l[0]&&_t(l);return{c(){e=g("div"),t=g("div"),r=g("button"),r.textContent=d,o=S(),n=g("button"),n.textContent=c,a=S(),f&&f.c(),i=S(),M&&M.c(),p=we(),this.h()},l(L){e=v(L,"DIV",{class:!0});var P=k(e);t=v(P,"DIV",{style:!0});var w=k(t);r=v(w,"BUTTON",{class:!0,"data-svelte-h":!0}),G(r)!=="svelte-osqxlt"&&(r.textContent=d),o=N(w),n=v(w,"BUTTON",{class:!0,"data-svelte-h":!0}),G(n)!=="svelte-ixdg7j"&&(n.textContent=c),w.forEach(m),a=N(P),f&&f.l(P),P.forEach(m),i=N(L),M&&M.l(L),p=we(),this.h()},h(){h(r,"class","upload-files svelte-ttfemr"),h(n,"class","conn-database svelte-ttfemr"),Se(t,"display","flex"),Se(t,"gap","10px"),h(e,"class","upload_button_div svelte-ttfemr")},m(L,P){H(L,e,P),u(e,t),u(t,r),u(t,o),u(t,n),u(e,a),~s&&y[s].m(e,null),H(L,i,P),M&&M.m(L,P),H(L,p,P),b=!0,_||(D=[W(ll,"keydown",l[20]),W(r,"click",l[12]),W(n,"click",l[13])],_=!0)},p(L,P){let w=s;s=E(L),s===w?~s&&y[s].p(L,P):(f&&(Ut(),Ne(y[w],1,1,()=>{y[w]=null}),Ot()),~s?(f=y[s],f?f.p(L,P):(f=y[s]=C[s](L),f.c()),Me(f,1),f.m(e,null)):f=null),L[0]?M?M.p(L,P):(M=_t(L),M.c(),M.m(p.parentNode,p)):M&&(M.d(1),M=null)},i(L){b||(Me(f),b=!0)},o(L){Ne(f),b=!1},d(L){L&&(m(e),m(i),m(p)),~s&&y[s].d(),M&&M.d(L),_=!1,ke(D)}}}let Ee=10;function Le(){this.classList.contains("truncate-text")?(this.classList.remove("truncate-text"),this.classList.add("full-text")):this.classList.add("truncate-text")}function hl(l){const e={};return l.forEach(t=>{const{directory:r,...d}=t;e[r]||(e[r]={directory:r,children:[],currentPage:1,itemsPerPage:5,collapsed:!0}),e[r].children.push(d)}),Object.values(e)}function bt(l,e,t){const r=(e-1)*t,d=r+t;return l.slice(r,d)}function yt(l,e,t){const r=(e-1)*t,d=r+t;return l.slice(r,d)}function Pe(l,e){return Math.ceil(l.length/e)}function xe(l,e){return Math.ceil(l.length/e)}function wt(l){if(!l||typeof l!="string")return l||"";const e=/(?:\/|\\|)([a-zA-Z0-9_\/-]+\.(?:html|csv))/gi;return l.match(e)?l.replace(e,t=>`<a href="#" onclick="openHtmlViewer('${t.startsWith("/")?t.substring(1):t}'); return false;" class="text-blue-600 hover:underline">Data Summary</a>`):l}function ml(l,e,t){let r,d;Ue(l,qt,A=>t(46,r=A)),Ue(l,Ae,A=>t(11,d=A));let o=!1,n="",c="File Viewer",a=!1,s=null,{data:f}=e;const i=()=>{Ae.set("fileUploader")},p=()=>{Ae.set("dataBases")};let b=0;Ie(()=>{t(5,b=f.transaction.filter(A=>A.doc_name).length)}),r.data.user.email,Ft.subscribe(A=>{A.email,A.org,A.role});let _=1,D=[];async function C(A,F,oe,Z){try{if((await Te.post("/delete_file",{org:A+"__"+F,doc_name:Z,directory:oe})).status===200)D=D.filter(me=>me.doc_name!==Z),window.location.reload();else throw new Ve("Failed to delete item")}catch(K){throw window.alert(K.response.data.message),K}}let y={};async function E(A,F,oe,Z,K,me){var be,O;try{let q=(_-1)*Ee+A;if(t(7,y[Z]=!0,y),me==="active"||me===void 0?t(9,w[q].children[0].isEnabled="inactive",w):t(9,w[q].children[0].isEnabled="active",w),t(9,w=[...w]),(await Te.post("/update_status",{org:F+"__"+oe,doc_name:K,directory:Z,isEnabled:me===void 0?"active":me})).status!==200)throw me==="active"||me===void 0?t(9,w[q].children[0].isEnabled="active",w):t(9,w[q].children[0].isEnabled="inactive",w),new Ve("Something went wrong")}catch(q){console.log(((O=(be=q.response)==null?void 0:be.data)==null?void 0:O.message)||q.message)}finally{t(7,y[Z]=!1,y),t(9,w=[...w])}}function M(A,F,oe,Z){window.confirm(`Are you sure you want to delete ${Z}`)&&C(A,F,oe,Z)}let L=hl(f.transaction),P="",w=L;function T(A){t(8,P=A.target.value.toLowerCase()),t(9,w=L.filter(F=>{const oe=F.directory&&typeof F.directory=="string"&&F.directory.toLowerCase().includes(P),Z=F.children.some(K=>K.doc_name&&typeof K.doc_name=="string"&&K.doc_name.toLowerCase().includes(P)||K.email&&typeof K.email=="string"&&K.email.toLowerCase().includes(P)||K.state&&typeof K.state=="string"&&K.state.toLowerCase().includes(P));return oe||Z}))}function I(A,F){A.currentPage=F,t(9,w=[...w])}function x(A,F){t(6,_=F),t(9,w=[...w])}function V(A){A.collapsed=!A.collapsed,t(9,w=[...w])}let B;function z(A){let F=A;!F.startsWith("html/")&&!F.startsWith("csv/")&&(F.endsWith(".html")?F=`html/${F}`:F.endsWith(".csv")&&(F=`csv/${F}`));const oe=`${Ht}${F}`;console.log("Opening file with URL:",oe),t(2,c=F.split("/").pop()||"File Viewer"),t(3,a=!0),t(4,s=null),t(0,o=!0),t(1,n="");let Z="";F.includes("#")&&([F,Z]=F.split("#"),Z="#"+Z),fetch(oe,{method:"GET",headers:{Authorization:`${Vt}`}}).then(K=>{if(!K.ok)throw new Ve(`Failed to load content: ${K.status} ${K.statusText}`);return K.text()}).then(K=>{t(3,a=!1);const me=`
					<!DOCTYPE html>
					<html>
					<head>
						<base target="_self">
						<style>
							/* Ensure smooth scrolling for anchor navigation */
							html {
								scroll-behavior: smooth;
							}
							/* Prevent body overflow in iframe */
							body {
								overflow-x: hidden;
								margin: 0;
								padding: 10px;
							}
						</style>
						<script>
							// Wait for DOM to be fully loaded
							document.addEventListener('DOMContentLoaded', function() {
								// Fix for select elements and other form controls
								const selectElements = document.querySelectorAll('select');
								selectElements.forEach(select => {
									// Clone and replace to remove any existing event listeners
									const newSelect = select.cloneNode(true);
									select.parentNode.replaceChild(newSelect, select);
									
									// Add our own change handler
									newSelect.addEventListener('change', function(e) {
										// Execute any inline onchange handler if present
										const onChangeAttr = this.getAttribute('onchange');
										if (onChangeAttr) {
											// Create a function from the onchange attribute and call it
											// in the context of the select element
											try {
												const handler = new Function('event', onChangeAttr);
												handler.call(this, e);
											} catch (error) {
												console.error('Error executing onchange handler:', error);
											}
										}
									});
								});
								
								// Handle all form inputs
								const formInputs = document.querySelectorAll('input, textarea');
								formInputs.forEach(input => {
									// Clone and replace to ensure clean event handling
									const newInput = input.cloneNode(true);
									input.parentNode.replaceChild(newInput, input);
									
									// Re-attach events like onchange, oninput, onkeyup
									['change', 'input', 'keyup'].forEach(eventType => {
										const handlerAttr = newInput.getAttribute('on' + eventType);
										if (handlerAttr) {
											newInput.addEventListener(eventType, function(e) {
												try {
													const handler = new Function('event', handlerAttr);
													handler.call(this, e);
												} catch (error) {
													console.error(\`Error executing \${eventType} handler:\`, error);
												}
											});
										}
									});
								});
								
								// Handle form submissions
								const forms = document.querySelectorAll('form');
								forms.forEach(form => {
									form.addEventListener('submit', function(e) {
										e.preventDefault();
										
										// Execute any onsubmit handler if present
										const onSubmitAttr = this.getAttribute('onsubmit');
										if (onSubmitAttr) {
											try {
												// If onsubmit returns false, don't proceed
												const handler = new Function('event', onSubmitAttr);
												const result = handler.call(this, e);
												if (result === false) return;
											} catch (error) {
												console.error('Error executing onsubmit handler:', error);
											}
										}
										
										console.log('Form submission handled in iframe');
									});
								});
							});
							
							// Handle all link clicks to prevent breaking out of iframe
							document.addEventListener('click', function(e) {
								const target = e.target.closest('a');
								if (target && target.getAttribute('href')) {
									const href = target.getAttribute('href');
									
									// Handle anchor links
									if (href.startsWith('#')) {
										e.preventDefault();
										const element = document.querySelector(href);
										if (element) {
											element.scrollIntoView({
												behavior: 'smooth',
												block: 'start'
											});
										}
									}
									// Handle relative links
									else if (!href.startsWith('http') && !href.startsWith('//')) {
										e.preventDefault();
										// Notify parent that we want to navigate
										window.parent.postMessage({
											type: 'navigate',
											path: href
										}, '*');
									}
								}
							}, true);
							
							// Set up a MutationObserver to handle dynamically added elements
							const observer = new MutationObserver(function(mutations) {
								mutations.forEach(function(mutation) {
									if (mutation.addedNodes && mutation.addedNodes.length > 0) {
										// Check for newly added form elements
										mutation.addedNodes.forEach(function(node) {
											if (node.querySelectorAll) {
												// Process any new select elements
												const newSelects = node.querySelectorAll('select');
												newSelects.forEach(select => {
													select.addEventListener('change', function(e) {
														const onChangeAttr = this.getAttribute('onchange');
														if (onChangeAttr) {
															try {
																const handler = new Function('event', onChangeAttr);
																handler.call(this, e);
															} catch (error) {
																console.error('Error executing onchange handler:', error);
															}
														}
													});
												});
											}
										});
									}
								});
							});
							
							// Start observing document body for changes
							observer.observe(document.body, { 
								childList: true, 
								subtree: true 
							});
							
							// Notify parent frame that we're ready
							window.parent.postMessage({ type: 'iframeReady' }, '*');
						<\/script>
					</head>
					<body>${K}</body>
					</html>`;t(1,n=me),Z&&B&&setTimeout(()=>{const be=B.contentWindow;be&&be.postMessage({type:"scrollToAnchor",anchor:Z},"*")},300)}).catch(K=>{console.error("Error fetching HTML file:",K),t(3,a=!1),t(4,s=K.message)})}function X(A){!A.data||typeof A.data!="object"||(A.data.type==="navigate"?z(A.data.path):A.data.type==="iframeReady"&&console.log("Iframe content fully loaded and initialized"))}Ie(()=>(window.addEventListener("message",X),window.openHtmlViewer=z,console.log("Checking for HTML/CSV files in summaries..."),document.querySelectorAll(".summary .truncate-text").forEach((F,oe)=>{const Z=F.innerHTML;(Z.includes(".html")||Z.includes(".csv"))&&console.log(`Found file reference in summary ${oe}:`,Z)}),()=>{window.removeEventListener("message",X)}));function ie(A){A.key==="Escape"&&o&&ne()}function ne(){t(0,o=!1),setTimeout(()=>{t(1,n=""),t(4,s=null)},300)}function ue(A){Ct.call(this,l,A)}function fe(){P=this.value,t(8,P)}const te=()=>window.location.reload(),le=A=>V(A),$=(A,F,oe)=>{oe.preventDefault(),E(A,F.children[0].org,F.children[0].group,F.directory,F.children[0].doc_name,F.children[0].isEnabled)},re=A=>V(A),se=(A,F)=>M(A.org,A.group,F.directory,A.doc_name),pe=A=>I(A,A.currentPage-1),R=(A,F)=>I(A,F+1),ce=A=>I(A,A.currentPage+1),ee=()=>x(w,_-1),U=A=>x(w,A+1),j=()=>x(w,_+1);function _e(A){Tt[A?"unshift":"push"](()=>{B=A,t(10,B)})}return l.$$set=A=>{"data"in A&&t(22,f=A.data)},[o,n,c,a,s,b,_,y,P,w,B,d,i,p,E,M,T,I,x,V,ie,ne,f,ue,fe,te,le,$,re,se,pe,R,ce,ee,U,j,_e]}class Ll extends Pt{constructor(e){super(),xt(this,e,ml,fl,kt,{data:22},null,[-1,-1,-1])}}export{Ll as component};
