import{s as me,b as pe,e as ue,a as ge,h as we,c as he,d as j,f as ye,g as L,i as _e,j as ke,k as Se,l as R,o as be,m as y}from"../chunks/scheduler.91cfa29b.js";import{S as Me,i as Ee,e as Ae,c as Ce,a as Oe,m as Te,t as Fe,b as Ie,d as Re}from"../chunks/index.03cf2e9a.js";import{f as Y,C as We,p as J,w as Ue,r as W}from"../chunks/pendingMessage.b23b3dc7.js";import{p as Ne}from"../chunks/stores.33557e05.js";import{g as Z,i as v}from"../chunks/navigation.90364bb7.js";import{b as U}from"../chunks/paths.e40a44b0.js";import{s as Le,t as x,U as $}from"../chunks/titleUpdate.4d61826c.js";import{e as u,E as X}from"../chunks/forms.ab03a9be.js";import{s as Pe}from"../chunks/sessionStore.520658ac.js";import{a as q}from"../chunks/axios.1f2f0c0e.js";function je(r){let g,s,h,n,p,f;document.title=g=r[8];function M(t){r[13](t)}let E={loading:r[6],pending:r[7],messages:r[2],llmResponse:r[4],shared:r[0].shared,preprompt:r[0].preprompt,models:r[0].models,currentModel:Y([...r[0].models,...r[0].oldModels],r[0].model),settings:r[0].settings};return r[5]!==void 0&&(E.webSearchMessages=r[5]),n=new We({props:E}),pe.push(()=>Ae(n,"webSearchMessages",M)),n.$on("message",r[10]),n.$on("retry",r[11]),n.$on("vote",r[14]),n.$on("share",r[15]),n.$on("stop",r[16]),{c(){s=ue("link"),h=ge(),Ce(n.$$.fragment),this.h()},l(t){const o=we("svelte-626amo",document.head);s=he(o,"LINK",{rel:!0,href:!0,integrity:!0,crossorigin:!0}),o.forEach(j),h=ye(t),Oe(n.$$.fragment,t),this.h()},h(){L(s,"rel","stylesheet"),L(s,"href","https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css"),L(s,"integrity","sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn"),L(s,"crossorigin","anonymous")},m(t,o){_e(document.head,s),ke(t,h,o),Te(n,t,o),f=!0},p(t,[o]){(!f||o&256)&&g!==(g=t[8])&&(document.title=g);const d={};o&64&&(d.loading=t[6]),o&128&&(d.pending=t[7]),o&4&&(d.messages=t[2]),o&16&&(d.llmResponse=t[4]),o&1&&(d.shared=t[0].shared),o&1&&(d.preprompt=t[0].preprompt),o&1&&(d.models=t[0].models),o&1&&(d.currentModel=Y([...t[0].models,...t[0].oldModels],t[0].model)),o&1&&(d.settings=t[0].settings),!p&&o&32&&(p=!0,d.webSearchMessages=t[5],Se(()=>p=!1)),n.$set(d)},i(t){f||(Fe(n.$$.fragment,t),f=!0)},o(t){Ie(n.$$.fragment,t),f=!1},d(t){t&&j(h),j(s),Re(n,t)}}}function Je(r,g,s){let h,n,p,f,M,E;R(r,Ne,e=>s(1,n=e)),R(r,J,e=>s(22,p=e)),R(r,u,e=>s(23,f=e)),R(r,x,e=>s(24,M=e)),R(r,Ue,e=>s(25,E=e));let{data:t}=g,o=t.messages,d=t.messages,A=!1,P=null,C="",O=null,_=!1,D,G="";Pe.subscribe(e=>{P=e.agenticWorkflow});let k=[],S=!1,N=!1;async function H(){try{s(6,S=!0);const e=await fetch(`${U}/conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fromShare:n.params.id,model:t.model})});if(!e.ok){u.set("Error while creating conversation, try again."),console.error("Error while creating conversation: "+await e.text());return}const{conversationId:a}=await e.json();return a}catch(e){throw u.set(X.default),console.error(String(e)),e}}async function T(e,a=W()){var l,c;if(e.trim())try{s(3,A=!1),s(6,S=!0),s(7,N=!0);let i=o.findIndex(F=>F.id===a);const z=i!==-1;z||(i=o.length),s(2,o=[...o.slice(0,i),{from:"user",content:e,id:a}]);const le=W(),b=await fetch(`${U}/conversation/${n.params.id}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({inputs:e,id:a,response_id:le,is_retry:z,web_search:E.useSearch,selected:localStorage.getItem("selected_docs")?JSON.parse(localStorage.getItem("selected_docs")||"[]"):[]})});if(!b.body)throw new Error("Body not defined");if(!b.ok){u.set((l=await b.json())==null?void 0:l.message);return}const ce=new TextDecoderStream,w=(c=b==null?void 0:b.body)==null?void 0:c.pipeThrough(ce).getReader();let Q="";for(;Q==="";){if(await new Promise(F=>setTimeout(F,25)),A){w==null||w.cancel();break}await(w==null?void 0:w.read().then(async({done:F,value:V})=>{if(F){w.cancel();return}if(!V)return;V.split(`
`).forEach(async fe=>{try{const m=JSON.parse(fe);if(m.type==="finalAnswer")Q=m.text,w.cancel(),v($.Conversation);else if(m.type==="stream"){s(7,N=!1);let I=o[o.length-1];I.from!=="assistant"?s(2,o=[...o,{from:"assistant",id:W(),content:m.token}]):(I.content+=m.token,s(2,o=[...o]))}else if(m.type==="webSearch")s(5,k=[...k,m]);else if(m.type==="status"&&m.status==="title"&&m.message){const I=t.conversations.find(({id:de})=>de===n.params.id);I&&(I.title=m.message,y(x,M={title:m.message,convId:n.params.id},M))}}catch{return}})}))}s(5,k=[]),await v($.ConversationList)}catch(i){i instanceof Error&&i.message.includes("overloaded")?y(u,f="Too much traffic, please try again.",f):i instanceof Error&&i.message.includes("429")?y(u,f=X.rateLimited,f):i instanceof Error?y(u,f=i.message,f):y(u,f=X.default,f),console.error(i)}finally{s(6,S=!1),s(7,N=!1)}}async function K(e,a){let l=n.params.id,c;s(2,o=o.map(i=>i.id===a?(c=i.score,{...i,score:e}):i));try{await fetch(`${U}/conversation/${l}/message/${a}/vote`,{method:"POST",body:JSON.stringify({score:e})})}catch{s(2,o=o.map(i=>i.id!==a?i:{...i,score:c}))}}async function B(e){var a;console.log("Agentic_AI Started."),_=!0,D=e,s(2,o=[...o,{from:"user",content:e,id:W()}]),s(2,o=[...o,{from:"assistant",content:"",id:W()}]);try{const l=await q.post("/agentic_ai",{prompt:e});if(O=(a=l==null?void 0:l.data)==null?void 0:a.workflow_id,!O){console.error("No workflow id available"),_=!1;return}const c=async()=>{try{const i=await ee(O);if(C==="initiated")setTimeout(()=>c(),3e3);else if(C==="success")te(O),console.log("Workflow completed successfully.");else{debugger;u.set("Error while creating workflow, try again."),console.warn("Unexpected workflow status:",C)}}catch(i){i.set("Error while creating workflow, try again."),console.error("Error checking workflow status:",i.message)}};await c(O)}catch(l){l.set("Error while creating workflow, try again."),console.error("errAgent:",l.response?l.response.data:l.message),_=!1}}async function ee(e){var a,l;console.log("get status api running..");try{const c=await q.post("/work_flow",{workFlowId:e});s(4,G=(a=c==null?void 0:c.data)==null?void 0:a.llm_response),C=(l=c==null?void 0:c.data)==null?void 0:l.status,_=C==="initiated"}catch(c){console.error("errWorkFlow:",c.response?c.response.data:c.message)}}async function te(e){console.log("get html data api running...");try{const a=await q.post("/workFlow_html",{workFlowId:e,userMsg:D});a!=null&&a.data&&s(2,o=o.map(l=>l.from==="assistant"&&!l.content?{...l,content:a.data}:l)),_=!1}catch(a){console.error("errWorkFlow:",a.response?a.response.data:a.message),_=!1}}be(async()=>{p&&(P==!0?(B(p),y(J,p="",p)):(await T(p),y(J,p="",p)))});async function se(e){t.shared?H().then(async a=>{await Z(`${U}/conversation/${a}`,{invalidateAll:!0})}).then(()=>T(e.detail)).finally(()=>s(6,S=!1)):P==!0?B(e.detail):T(e.detail)}async function oe(e){t.shared?H().then(async a=>{await Z(`${U}/conversation/${a}`,{invalidateAll:!0})}).then(()=>T(e.detail.content,e.detail.id)).finally(()=>s(6,S=!1)):T(e.detail.content,e.detail.id)}function re(e){k=e,s(5,k)}const ae=e=>K(e.detail.score,e.detail.id),ne=()=>Le(n.params.id,t.title),ie=()=>s(3,A=!0);return r.$$set=e=>{"data"in e&&s(0,t=e.data)},r.$$.update=()=>{var e;r.$$.dirty&4097&&t.messages!==d&&(s(2,o=t.messages),s(12,d=t.messages)),r.$$.dirty&2&&(n.params.id,s(3,A=!0)),r.$$.dirty&3&&s(8,h=((e=t.conversations.find(a=>a.id===n.params.id))==null?void 0:e.title)??t.title)},[t,n,o,A,G,k,S,N,h,K,se,oe,d,re,ae,ne,ie]}class Ze extends Me{constructor(g){super(),Ee(this,g,Je,je,me,{data:0})}}export{Ze as component};
