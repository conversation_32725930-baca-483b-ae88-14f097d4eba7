{"name": "accure-securegpt-backend", "version": "1.0.0", "description": "Backend server for Accure SecureGPT chat application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "init-db": "node scripts/init-db.js"}, "keywords": ["ai", "chat", "backend", "api"], "author": "Accure AI", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}