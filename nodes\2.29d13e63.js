import{s as g}from"../chunks/scheduler.91cfa29b.js";import{S as p,i as u,c as h,a as w,m as _,t as M,b as v,d as $}from"../chunks/index.03cf2e9a.js";import{g as S}from"../chunks/navigation.90364bb7.js";import{b as l}from"../chunks/paths.e40a44b0.js";import{C,f as m,p as E}from"../chunks/pendingMessage.b23b3dc7.js";import{e as d,E as y}from"../chunks/forms.ab03a9be.js";function b(o){let t,s;return t=new C({props:{loading:o[1],currentModel:m([...o[0].models,...o[0].oldModels],o[0].settings.activeModel),models:o[0].models,settings:o[0].settings}}),t.$on("message",o[3]),{c(){h(t.$$.fragment)},l(e){w(t.$$.fragment,e)},m(e,n){_(t,e,n),s=!0},p(e,[n]){const a={};n&2&&(a.loading=e[1]),n&1&&(a.currentModel=m([...e[0].models,...e[0].oldModels],e[0].settings.activeModel)),n&1&&(a.models=e[0].models),n&1&&(a.settings=e[0].settings),t.$set(a)},i(e){s||(M(t.$$.fragment,e),s=!0)},o(e){v(t.$$.fragment,e),s=!1},d(e){$(t,e)}}}function O(o,t,s){let{data:e}=t,n=!1;async function a(r){try{s(1,n=!0);const i=await fetch(`${l}/conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({model:e.settings.activeModel,preprompt:e.settings.customPrompts[e.settings.activeModel],selected:"a"})});if(!i.ok){d.set("Error while creating conversation, try again."),console.error("Error while creating conversation: "+await i.text());return}const{conversationId:f}=await i.json();E.set(r),await S(`${l}/conversation/${f}`,{invalidateAll:!0})}catch(i){d.set(y.default),console.error(i)}finally{s(1,n=!1)}}const c=r=>a(r.detail);return o.$$set=r=>{"data"in r&&s(0,e=r.data)},[e,n,a,c]}class q extends p{constructor(t){super(),u(this,t,O,b,g,{data:0})}}export{q as component};
