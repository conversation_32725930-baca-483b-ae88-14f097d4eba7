import{w as V}from"./paths.e40a44b0.js";import{s as w,a6 as b,K as G,H as $,L as q,n as v,E as K,d as p,a7 as k,j as y,u as h,a8 as A,F as z,l as Z,o as S,W as tt,a9 as U,e as M,c as x,g as _,aa as Y,ab as J,ac as W,ad as Q,b as N,ae as et,i as nt,r as P,A as at,v as st}from"./scheduler.91cfa29b.js";import{S as E,i as L,t as C,b as D,c as ot,a as rt,m as it,d as lt,h as O}from"./index.03cf2e9a.js";import{p as ct}from"./stores.33557e05.js";import{b as ut}from"./public.44f51b11.js";import{h as dt}from"./handlebars.runtime.6449bbf2.js";import{p as ft}from"./parse.bee59afc.js";import{j as mt}from"./singletons.33e23ca4.js";import{a as ht}from"./navigation.90364bb7.js";function X(n,t){const o={},i={},a={$$scope:1};let e=n.length;for(;e--;){const s=n[e],r=t[e];if(r){for(const u in s)u in r||(i[u]=1);for(const u in r)a[u]||(o[u]=r[u],a[u]=1);n[e]=r}else for(const u in s)a[u]=1}for(const s in i)s in o||(o[s]=void 0);return o}const Rt={default:"Oops, something went wrong.",authOnly:"You have to be logged in.",rateLimited:"You are sending too many messages. Try again later."},Vt=V(null);function pt(n){let t,o,i='<path fill="currentColor" d="M17.414 16L24 9.414L22.586 8L16 14.586L9.414 8L8 9.414L14.586 16L8 22.586L9.414 24L16 17.414L22.586 24L24 22.586z"/>',a=[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},n[0]],e={};for(let s=0;s<a.length;s+=1)e=b(e,a[s]);return{c(){t=G("svg"),o=new $(!0),this.h()},l(s){t=q(s,"svg",{viewBox:!0,width:!0,height:!0});var r=v(t);o=K(r,!0),r.forEach(p),this.h()},h(){o.a=null,k(t,e)},m(s,r){y(s,t,r),o.m(i,t)},p(s,[r]){k(t,e=X(a,[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},r&1&&s[0]]))},i:h,o:h,d(s){s&&p(t)}}}function _t(n,t,o){return n.$$set=i=>{o(0,t=b(b({},t),A(i)))},t=A(t),[t]}class Gt extends E{constructor(t){super(),L(this,t,_t,pt,w,{})}}function gt(n){return{c:h,l:h,m:h,p:h,d:h}}function bt(n){let t;function o(e,s){return gt}let a=o()(n);return{c(){a.c(),t=z()},l(e){a.l(e),t=z()},m(e,s){a.m(e,s),y(e,t,s)},p(e,[s]){a.p(e,s)},i:h,o:h,d(e){e&&p(t),a.d(e)}}}function vt(n,t,o){let i;Z(n,ct,r=>o(2,i=r));let{classNames:a=""}=t,e="",s=ut;return S(()=>{e=window.location.hostname,e.includes("securegpt.accure.ai")?o(1,s="SecureGPT"):e.includes("legalgpt.accure.ai")&&o(1,s="LegalGPT")}),n.$$set=r=>{"classNames"in r&&o(0,a=r.classNames)},[a,s,i]}class $t extends E{constructor(t){super(),L(this,t,vt,bt,w,{classNames:0})}}function wt(n){let t,o,i='<path fill="currentColor" d="M2 26h28v2H2zM25.4 9c.8-.8.8-2 0-2.8l-3.6-3.6c-.8-.8-2-.8-2.8 0l-15 15V24h6.4zm-5-5L24 7.6l-3 3L17.4 7zM6 22v-3.6l10-10l3.6 3.6l-10 10z"/>',a=[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},n[0]],e={};for(let s=0;s<a.length;s+=1)e=b(e,a[s]);return{c(){t=G("svg"),o=new $(!0),this.h()},l(s){t=q(s,"svg",{viewBox:!0,width:!0,height:!0});var r=v(t);o=K(r,!0),r.forEach(p),this.h()},h(){o.a=null,k(t,e)},m(s,r){y(s,t,r),o.m(i,t)},p(s,[r]){k(t,e=X(a,[{viewBox:"0 0 32 32"},{width:"1.2em"},{height:"1.2em"},r&1&&s[0]]))},i:h,o:h,d(s){s&&p(t)}}}function yt(n,t,o){return n.$$set=i=>{o(0,t=b(b({},t),A(i)))},t=A(t),[t]}class qt extends E{constructor(t){super(),L(this,t,yt,wt,w,{})}}dt.default;const Et=typeof window<"u"&&localStorage.getItem("assistantName")||"",Lt=V(Et);typeof window<"u"&&Lt.subscribe(n=>{localStorage.setItem("assistantName",n)});function F(n){const t=n-1;return t*t*t+1}function j(n,{delay:t=0,duration:o=400,easing:i=tt}={}){const a=+getComputedStyle(n).opacity;return{delay:t,duration:o,easing:i,css:e=>`opacity: ${e*a}`}}function kt(n){let t,o;const i=n[2].default,a=U(i,n,n[1],null);return{c(){t=M("div"),a&&a.c(),this.h()},l(e){t=x(e,"DIV",{class:!0});var s=v(t);a&&a.l(s),s.forEach(p),this.h()},h(){_(t,"class","contents"),t.hidden=!0},m(e,s){y(e,t,s),a&&a.m(t,null),n[3](t),o=!0},p(e,[s]){a&&a.p&&(!o||s&2)&&Y(a,i,e,e[1],o?W(i,e[1],s,null):J(e[1]),null)},i(e){o||(C(a,e),o=!0)},o(e){D(a,e),o=!1},d(e){e&&p(t),a&&a.d(e),n[3](null)}}}function At(n,t,o){let{$$slots:i={},$$scope:a}=t,e;S(()=>{e.ownerDocument.body.appendChild(e)}),Q(()=>{e!=null&&e.parentNode&&e.parentNode.removeChild(e)});function s(r){N[r?"unshift":"push"](()=>{e=r,o(0,e)})}return n.$$set=r=>{"$$scope"in r&&o(1,a=r.$$scope)},[e,a,i,s]}class Mt extends E{constructor(t){super(),L(this,t,At,kt,w,{})}}function xt(n){let t,o,i,a,e,s,r;const u=n[5].default,d=U(u,n,n[8],null);return{c(){t=M("div"),o=M("div"),d&&d.c(),this.h()},l(c){t=x(c,"DIV",{role:!0,tabindex:!0,class:!0});var m=v(t);o=x(m,"DIV",{role:!0,tabindex:!0,class:!0});var g=v(o);d&&d.l(g),g.forEach(p),m.forEach(p),this.h()},h(){_(o,"role","dialog"),_(o,"tabindex","-1"),_(o,"class",i="max-h-[90dvh] overflow-y-auto overflow-x-hidden rounded-2xl bg-white shadow-2xl outline-none sm:-mt-10 "+n[0]),_(t,"role","presentation"),_(t,"tabindex","-1"),_(t,"class","fixed inset-0 z-40 flex items-center justify-center bg-black/80 p-8 backdrop-blur-sm dark:bg-black/50")},m(c,m){y(c,t,m),nt(t,o),d&&d.m(o,null),n[6](o),n[7](t),e=!0,s||(r=[P(o,"keydown",n[3]),P(t,"click",n[4])],s=!0)},p(c,m){d&&d.p&&(!e||m&256)&&Y(d,u,c,c[8],e?W(u,c[8],m,null):J(c[8]),null),(!e||m&1&&i!==(i="max-h-[90dvh] overflow-y-auto overflow-x-hidden rounded-2xl bg-white shadow-2xl outline-none sm:-mt-10 "+c[0]))&&_(o,"class",i)},i(c){e||(C(d,c),at(()=>{e&&(a||(a=O(t,j,{easing:F,duration:300},!0)),a.run(1))}),e=!0)},o(c){D(d,c),a||(a=O(t,j,{easing:F,duration:300},!1)),a.run(0),e=!1},d(c){c&&p(t),d&&d.d(c),n[6](null),n[7](null),c&&a&&a.end(),s=!1,st(r)}}}function Nt(n){let t,o;return t=new Mt({props:{$$slots:{default:[xt]},$$scope:{ctx:n}}}),{c(){ot(t.$$.fragment)},l(i){rt(t.$$.fragment,i)},m(i,a){it(t,i,a),o=!0},p(i,[a]){const e={};a&263&&(e.$$scope={dirty:a,ctx:i}),t.$set(e)},i(i){o||(C(t.$$.fragment,i),o=!0)},o(i){D(t.$$.fragment,i),o=!1},d(i){lt(t,i)}}}function St(n,t,o){let{$$slots:i={},$$scope:a}=t,{width:e="max-w-sm"}=t,s,r;const u=et();function d(l){l.key==="Escape"&&(l.preventDefault(),u("close"))}function c(l){l.target===s&&u("close")}S(()=>{var l;(l=document.getElementById("app"))==null||l.setAttribute("inert","true"),r.focus()}),Q(()=>{var l;document.querySelectorAll('[role="dialog"]:not(#app *)').length===1&&((l=document.getElementById("app"))==null||l.removeAttribute("inert"))});function m(l){N[l?"unshift":"push"](()=>{r=l,o(2,r)})}function g(l){N[l?"unshift":"push"](()=>{s=l,o(1,s)})}return n.$$set=l=>{"width"in l&&o(0,e=l.width),"$$scope"in l&&o(8,a=l.$$scope)},[e,s,r,d,c,i,m,g,a]}class Kt extends E{constructor(t){super(),L(this,t,St,Nt,w,{width:0})}}function Ct(n){return mt.apply_action(n)}function Dt(n){const t=JSON.parse(n);return t.data&&(t.data=ft(t.data)),t}function R(n){return HTMLElement.prototype.cloneNode.call(n)}function Ut(n,t=()=>{}){const o=async({action:a,result:e,reset:s=!0,invalidateAll:r=!0})=>{e.type==="success"&&(s&&HTMLFormElement.prototype.reset.call(n),r&&await ht()),(location.origin+location.pathname===a.origin+a.pathname||e.type==="redirect"||e.type==="error")&&Ct(e)};async function i(a){var B,T,H,I;if(((B=a.submitter)!=null&&B.hasAttribute("formmethod")?a.submitter.formMethod:R(n).method)!=="post")return;a.preventDefault();const s=new URL((T=a.submitter)!=null&&T.hasAttribute("formaction")?a.submitter.formAction:R(n).action),r=new FormData(n),u=(H=a.submitter)==null?void 0:H.getAttribute("name");u&&r.append(u,((I=a.submitter)==null?void 0:I.getAttribute("value"))??"");const d=new AbortController;let c=!1;const g=await t({action:s,cancel:()=>c=!0,controller:d,get data(){return r},formData:r,get form(){return n},formElement:n,submitter:a.submitter})??o;if(c)return;let l;try{const f=await fetch(s,{method:"POST",headers:{accept:"application/json","x-sveltekit-action":"true"},cache:"no-store",body:r,signal:d.signal});l=Dt(await f.text()),l.type==="error"&&(l.status=f.status)}catch(f){if((f==null?void 0:f.name)==="AbortError")return;l={type:"error",error:f}}g({action:s,get data(){return r},formData:r,get form(){return n},formElement:n,update:f=>o({action:s,result:l,reset:f==null?void 0:f.reset,invalidateAll:f==null?void 0:f.invalidateAll}),result:l})}return HTMLFormElement.prototype.addEventListener.call(n,"submit",i),{destroy(){HTMLFormElement.prototype.removeEventListener.call(n,"submit",i)}}}export{Gt as C,Rt as E,$t as L,Kt as M,qt as a,Lt as b,Ut as c,Vt as e,j as f,X as g};
