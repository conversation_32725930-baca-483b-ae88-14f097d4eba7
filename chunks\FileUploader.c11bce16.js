import{s as fe,e as I,a as B,F as ee,c as w,n as N,p as J,f as A,d as b,g as m,S as Z,w as he,j as O,i as h,y as te,r as j,u as x,v as me,l as ve,t as q,q as G,x as Y,b as se}from"./scheduler.91cfa29b.js";import{e as le,u as _e,d as ge}from"./each.6f0e5b78.js";import{S as ye,i as be}from"./index.03cf2e9a.js";import{w as ke}from"./paths.e40a44b0.js";import{a as ae}from"./axios.1f2f0c0e.js";let Q;const De=new Uint8Array(16);function Ie(){if(!Q&&(Q=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Q))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Q(De)}const D=[];for(let t=0;t<256;++t)D.push((t+256).toString(16).slice(1));function we(t,e=0){return D[t[e+0]]+D[t[e+1]]+D[t[e+2]]+D[t[e+3]]+"-"+D[t[e+4]]+D[t[e+5]]+"-"+D[t[e+6]]+D[t[e+7]]+"-"+D[t[e+8]]+D[t[e+9]]+"-"+D[t[e+10]]+D[t[e+11]]+D[t[e+12]]+D[t[e+13]]+D[t[e+14]]+D[t[e+15]]}const Ue=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),ne={randomUUID:Ue};function Ce(t,e,l){if(ne.randomUUID&&!e&&!t)return ne.randomUUID();t=t||{};const s=t.random||(t.rng||Ie)();if(s[6]=s[6]&15|64,s[8]=s[8]&63|128,e){l=l||0;for(let a=0;a<16;++a)e[l+a]=s[a];return e}return we(s)}function ie(t,e,l){const s=t.slice();return s[23]=e[l],s}function oe(t){let e,l="Please enter a directory name before uploading files.";return{c(){e=I("p"),e.textContent=l,this.h()},l(s){e=w(s,"P",{class:!0,"data-svelte-h":!0}),J(e)!=="svelte-1pvdr99"&&(e.textContent=l),this.h()},h(){m(e,"class","error-message svelte-1o88pay")},m(s,a){O(s,e,a)},d(s){s&&b(e)}}}function re(t){let e,l,s,a=[],p=new Map,i=t[6]&&de(),c=le(t[7]);const U=o=>o[23].id;for(let o=0;o<c.length;o+=1){let n=ie(t,c,o),_=U(n);p.set(_,a[o]=ue(_,n))}return{c(){e=I("div"),i&&i.c(),l=B(),s=I("div");for(let o=0;o<a.length;o+=1)a[o].c();this.h()},l(o){e=w(o,"DIV",{class:!0});var n=N(e);i&&i.l(n),l=A(n),s=w(n,"DIV",{class:!0});var _=N(s);for(let k=0;k<a.length;k+=1)a[k].l(_);_.forEach(b),n.forEach(b),this.h()},h(){m(s,"class","upload-list svelte-1o88pay"),m(e,"class","right-section svelte-1o88pay")},m(o,n){O(o,e,n),i&&i.m(e,null),h(e,l),h(e,s);for(let _=0;_<a.length;_+=1)a[_]&&a[_].m(s,null)},p(o,n){o[6]?i||(i=de(),i.c(),i.m(e,l)):i&&(i.d(1),i=null),n&196736&&(c=le(o[7]),a=_e(a,n,U,1,o,c,p,s,ge,ue,null,ie))},d(o){o&&b(e),i&&i.d();for(let n=0;n<a.length;n+=1)a[n].d()}}}function de(t){let e,l="⚠️ Large image files (>500 KB) may not work optimally with the system";return{c(){e=I("div"),e.textContent=l,this.h()},l(s){e=w(s,"DIV",{class:!0,"data-svelte-h":!0}),J(e)!=="svelte-xsuvtf"&&(e.textContent=l),this.h()},h(){m(e,"class","large-files-warning svelte-1o88pay")},m(s,a){O(s,e,a)},d(s){s&&b(e)}}}function ue(t,e){let l,s,a,p=e[17](e[23].name)+"",i,c,U=e[16](e[23].size)+"",o,n,_,k,y,z=e[23].status+"",S,F,L;return{key:t,first:null,c(){l=I("div"),s=I("div"),a=I("strong"),i=q(p),c=q(" ("),o=q(U),n=q(")"),_=B(),k=I("div"),y=I("span"),S=q(z),L=B(),this.h()},l(E){l=w(E,"DIV",{class:!0});var r=N(l);s=w(r,"DIV",{class:!0});var f=N(s);a=w(f,"STRONG",{});var v=N(a);i=G(v,p),v.forEach(b),c=G(f," ("),o=G(f,U),n=G(f,")"),f.forEach(b),_=A(r),k=w(r,"DIV",{class:!0});var u=N(k);y=w(u,"SPAN",{class:!0});var g=N(y);S=G(g,z),g.forEach(b),u.forEach(b),L=A(r),r.forEach(b),this.h()},h(){m(s,"class","file-info svelte-1o88pay"),m(y,"class",F="status-"+(e[23].status==="Uploaded"?"uploaded":e[23].status.includes("Warning")||e[23].status.includes("⚠️")?"warning":"uploading")+" svelte-1o88pay"),m(k,"class","status-container svelte-1o88pay"),m(l,"class","upload-item svelte-1o88pay"),this.first=l},m(E,r){O(E,l,r),h(l,s),h(s,a),h(a,i),h(s,c),h(s,o),h(s,n),h(l,_),h(l,k),h(k,y),h(y,S),h(l,L)},p(E,r){e=E,r&128&&p!==(p=e[17](e[23].name)+"")&&Y(i,p),r&128&&U!==(U=e[16](e[23].size)+"")&&Y(o,U),r&128&&z!==(z=e[23].status+"")&&Y(S,z),r&128&&F!==(F="status-"+(e[23].status==="Uploaded"?"uploaded":e[23].status.includes("Warning")||e[23].status.includes("⚠️")?"warning":"uploading")+" svelte-1o88pay")&&m(y,"class",F)},d(E){E&&b(l)}}}function pe(t){let e,l="Done",s,a;return{c(){e=I("button"),e.textContent=l,this.h()},l(p){e=w(p,"BUTTON",{class:!0,"data-svelte-h":!0}),J(e)!=="svelte-1ombwlo"&&(e.textContent=l),this.h()},h(){m(e,"class","done-button svelte-1o88pay")},m(p,i){O(p,e,i),s||(a=j(e,"click",t[12]),s=!0)},p:x,d(p){p&&b(e),s=!1,a()}}}function Fe(t){let e,l,s,a,p="Directory Name:",i,c,U,o,n,_=`Drag and drop or <span class="browse-link">browse files</span> to upload. Supported formats:
			<br/><b>Text</b>: txt, pdf, docx, pptx |
			<b>Images</b>: png, jpg, jpeg, webp |
			<b>Video</b>: mp4, mov, avi, mkv |
			<br/><b>Audio</b>: mp3, wav, aac,|
			<b>Structured</b>: csv, psv, tsv |
			<b>Compressed</b>: zip, rar, 7z, tar`,k,y,z,S,F,L,E,r=t[3]&&oe(),f=t[4]&&re(t),v=t[2]&&pe(t);return{c(){e=I("div"),l=I("div"),s=I("div"),a=I("label"),a.textContent=p,i=B(),c=I("input"),U=B(),r&&r.c(),o=B(),n=I("div"),n.innerHTML=_,k=B(),y=I("input"),z=B(),f&&f.c(),S=B(),v&&v.c(),F=ee(),this.h()},l(u){e=w(u,"DIV",{class:!0});var g=N(e);l=w(g,"DIV",{class:!0});var R=N(l);s=w(R,"DIV",{class:!0});var P=N(s);a=w(P,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),J(a)!=="svelte-dyxtj0"&&(a.textContent=p),i=A(P),c=w(P,"INPUT",{type:!0,id:!0,class:!0,placeholder:!0}),U=A(P),r&&r.l(P),P.forEach(b),o=A(R),n=w(R,"DIV",{class:!0,"data-svelte-h":!0}),J(n)!=="svelte-xfjl2s"&&(n.innerHTML=_),k=A(R),y=w(R,"INPUT",{type:!0,class:!0,style:!0}),R.forEach(b),z=A(g),f&&f.l(g),g.forEach(b),S=A(u),v&&v.l(u),F=ee(),this.h()},h(){m(a,"for","directoryInput"),m(a,"class","directory-label svelte-1o88pay"),m(c,"type","text"),m(c,"id","directoryInput"),m(c,"class","directory-input dark-input svelte-1o88pay"),m(c,"placeholder","Enter directory name"),m(s,"class","directory-section svelte-1o88pay"),m(n,"class","dropzone svelte-1o88pay"),Z(n,"expanded",!t[4]),Z(n,"collapsed",t[4]),m(y,"type","file"),y.multiple=!0,m(y,"class","hidden-file-input"),he(y,"display","none"),m(l,"class","left-section svelte-1o88pay"),m(e,"class","container svelte-1o88pay")},m(u,g){O(u,e,g),h(e,l),h(l,s),h(s,a),h(s,i),h(s,c),te(c,t[0]),h(s,U),r&&r.m(s,null),h(l,o),h(l,n),t[20](n),h(l,k),h(l,y),t[21](y),h(e,z),f&&f.m(e,null),O(u,S,g),v&&v.m(u,g),O(u,F,g),L||(E=[j(c,"input",t[18]),j(c,"blur",t[13]),j(c,"input",t[19]),j(n,"dragover",t[10]),j(n,"drop",t[9]),j(n,"dragleave",t[11]),j(n,"click",t[14]),j(y,"change",t[15])],L=!0)},p(u,[g]){g&1&&c.value!==u[0]&&te(c,u[0]),u[3]?r||(r=oe(),r.c(),r.m(s,null)):r&&(r.d(1),r=null),g&16&&Z(n,"expanded",!u[4]),g&16&&Z(n,"collapsed",u[4]),u[4]?f?f.p(u,g):(f=re(u),f.c(),f.m(e,null)):f&&(f.d(1),f=null),u[2]?v?v.p(u,g):(v=pe(u),v.c(),v.m(F.parentNode,F)):v&&(v.d(1),v=null)},i:x,o:x,d(u){u&&(b(e),b(S),b(F)),r&&r.d(),t[20](null),t[21](null),f&&f.d(),v&&v.d(u),L=!1,me(E)}}}function Ee(t,e,l){let s,a="",p=ke([]);ve(t,p,d=>l(7,s=d));let i,c=!1,U=!1,o=!1,n,_=!1;const k=async(d,M)=>{if(!M||M.trim()===""){l(3,U=!0);return}const $=Array.from(d).map(C=>{const W=C.type.startsWith("image/"),T=W&&C.size>500*1024;return T&&l(6,_=!0),{id:Ce(),fileObject:C,name:C.name,size:C.size,isImage:W,status:T?"Warning: Image exceeds 500 KB":"Uploading..."}});l(4,o=!0),p.update(C=>[...C,...$]);let K=!0;for(let C of $){const W=new FormData;W.append("file",C.fileObject),W.append("filename",C.name),W.append("directory",M);try{const T=await ae.post("/docs/upload",W,{headers:{"Content-Type":"multipart/form-data"}});if(T&&T.status===200&&!(T.data.code&&T.data.code==413)){p.update(ce=>ce.map(X=>X.id===C.id?{...X,status:C.isImage&&C.size>500*1024?"Uploaded ⚠️":"Uploaded"}:X));const H=JSON.stringify(T.data);await ae.post("/docs/upload/db",H,{headers:{"Content-Type":"application/json"}})||(K=!1)}else T.data.code&&T.data.code==413?(p.update(H=>H.map(V=>V.id===C.id?{...V,status:"File size limit exceeded"}:V)),K=!1):(p.update(H=>H.map(V=>V.id===C.id?{...V,status:"Failed to upload"}:V)),K=!1)}catch{p.update(H=>H.map(V=>V.id===C.id?{...V,status:"Error during upload"}:V)),K=!1}}l(2,c=K)},y=d=>{d.preventDefault(),d.stopPropagation();const M=d.dataTransfer.files;k(M,a)},z=d=>{d.preventDefault(),d.stopPropagation(),i.classList.add("drag-over")},S=()=>{i.classList.remove("drag-over")},F=async()=>{try{window.location.href="/docs",location.reload()}catch(d){console.error("Error navigating to /docs and reloading the page",d)}},L=()=>{a&&l(0,a=a.replace(/\s+/g,"_").replace(/[^a-zA-Z0-9_]/g,"_"))},E=()=>{n.click()},r=d=>{const M=d.target.files;k(M,a)},f=d=>d<1024?d+" B":d<1048576?(d/1024).toFixed(1)+" KB":(d/1048576).toFixed(1)+" MB",v=d=>d.length>50?d.slice(0,22)+"...":d;function u(){a=this.value,l(0,a)}const g=()=>l(3,U=!1);function R(d){se[d?"unshift":"push"](()=>{i=d,l(1,i)})}function P(d){se[d?"unshift":"push"](()=>{n=d,l(5,n)})}return[a,i,c,U,o,n,_,s,p,y,z,S,F,L,E,r,f,v,u,g,R,P]}class je extends ye{constructor(e){super(),be(this,e,Ee,Fe,fe,{})}}export{je as F};
