import{s as w,e as h,t as j,a as D,c as p,n as g,q as I,d as c,f as V,g as u,j as H,i as o,x as $,u as q,l as S}from"../chunks/scheduler.91cfa29b.js";import{S as C,i as z}from"../chunks/index.03cf2e9a.js";import{p as A}from"../chunks/stores.33557e05.js";function B(d){var y;let t,e,a,l=d[0].status+"",x,v,m,b,i,f=((y=d[0].error)==null?void 0:y.message)+"",_;return{c(){t=h("div"),e=h("div"),a=h("h1"),x=j(l),v=D(),m=h("div"),b=D(),i=h("h2"),_=j(f),this.h()},l(r){t=p(r,"DIV",{class:!0});var n=g(t);e=p(n,"DIV",{class:!0});var s=g(e);a=p(s,"H1",{class:!0});var E=g(a);x=I(E,l),E.forEach(c),v=V(s),m=p(s,"DIV",{class:!0}),g(m).forEach(c),b=V(s),i=p(s,"H2",{class:!0});var k=g(i);_=I(k,f),k.forEach(c),s.forEach(c),n.forEach(c),this.h()},h(){u(a,"class","mb-2 text-5xl font-semibold"),u(m,"class","-mx-8 my-2 h-px bg-gray-200 dark:bg-gray-700"),u(i,"class","max-w-sm text-lg"),u(e,"class","align-center -mt-24 flex flex-col justify-center rounded-xl border bg-white px-8 pb-2 pt-4 text-center dark:border-gray-700 dark:bg-gray-800"),u(t,"class","flex items-center justify-center bg-gradient-to-t from-gray-200 text-gray-800 dark:from-gray-700 dark:text-gray-300")},m(r,n){H(r,t,n),o(t,e),o(e,a),o(a,x),o(e,v),o(e,m),o(e,b),o(e,i),o(i,_)},p(r,[n]){var s;n&1&&l!==(l=r[0].status+"")&&$(x,l),n&1&&f!==(f=((s=r[0].error)==null?void 0:s.message)+"")&&$(_,f)},i:q,o:q,d(r){r&&c(t)}}}function F(d,t,e){let a;return S(d,A,l=>e(0,a=l)),[a]}class L extends C{constructor(t){super(),z(this,t,F,B,w,{})}}export{L as component};
